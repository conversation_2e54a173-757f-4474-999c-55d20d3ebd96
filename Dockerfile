FROM gitlab.isdd.sk:4567/isdd-docker/okd/**********************-alpine:1-0-1-jdk21-os3-22

WORKDIR /opt/apps
COPY target/dr-be-app.jar /opt/apps/dr.jar

USER root

RUN chown -R root:root /opt/apps/ && \
  chmod 774 -R /opt/apps

USER 1001

#ENTRYPOINT ["java", "-XX:+UseContainerSupport", "-XX:InitialRAMPercentage=70", "-XX:MaxRAMPercentage=70", "-jar", "/opt/apps/dr.jar"]
ENTRYPOINT ["/bin/sh", "-c", "exec java $JAVA_OPTS -jar /opt/apps/dr.jar"]
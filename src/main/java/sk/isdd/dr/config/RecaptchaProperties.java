package sk.isdd.dr.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Component
@ConfigurationProperties(prefix = "recaptcha")
@Getter
@Setter
public class RecaptchaProperties {

    private String secretKey;
    private String verifyUrl;
    private boolean enabled = true;
    private double scoreThreshold = 0.5;

}

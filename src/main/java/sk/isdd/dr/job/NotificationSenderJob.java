package sk.isdd.dr.job;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import sk.isdd.dr.notification.Notification;

@Service
public class NotificationSenderJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationSenderJob.class);

    private final List<Notification> notifications;

    public NotificationSenderJob(List<Notification> notifications) {
        this.notifications = notifications;
    }

    @Scheduled(cron = "${schedule.notification-sender}")
    @Transactional
    public void run() {
        LOGGER.info("Job [NotificationSenderJob] started");
        notifications.forEach(Notification::send);
    }

}

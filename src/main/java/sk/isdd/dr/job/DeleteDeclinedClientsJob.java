package sk.isdd.dr.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import sk.isdd.dr.api.service.api.SubjectService;

@Service
public class DeleteDeclinedClientsJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeleteDeclinedClientsJob.class);

    @Autowired
    private SubjectService subjectService;

    @Scheduled(cron = "${schedule.delete-declined-clients}")
    @Transactional
    public void run() {
        LOGGER.info("Job [DeleteDeclinedClients] started");
        subjectService.deleteDeclinedSubjects();
    }

}

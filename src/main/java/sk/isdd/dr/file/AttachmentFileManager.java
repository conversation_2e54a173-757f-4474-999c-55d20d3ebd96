package sk.isdd.dr.file;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import sk.isdd.dr.api.exception.system.SystemException;

@Service
public class AttachmentFileManager extends FileManager {

    @Value("${attachment.root-path}")
    private String attachmentsRootPath;

    @Override
    @PostConstruct
    public void init() {
        try {
            baseDirectory = Path.of(attachmentsRootPath);

            if (!Files.exists(baseDirectory)) {
                Files.createDirectories(baseDirectory);
            }

        } catch (IOException e) {
            throw new SystemException("Init AttachmentFileManager failed");
        }
    }

}

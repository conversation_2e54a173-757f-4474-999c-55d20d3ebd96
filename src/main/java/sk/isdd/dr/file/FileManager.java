package sk.isdd.dr.file;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.AccessPermission;
import org.apache.pdfbox.pdmodel.encryption.StandardProtectionPolicy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import sk.isdd.dr.api.exception.system.SystemException;

public abstract class FileManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileManager.class);

    protected Path baseDirectory;

    public abstract void init();

    public Path saveFile(String filename, byte[] data, String encryptPassword) {
        try {
            Path filePath = baseDirectory.resolve(filename).normalize();
            if (!filePath.startsWith(baseDirectory)) {
                throw new IllegalArgumentException("Invalid file path");
            }

            Path parentDir = filePath.getParent();

            if (!Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }

            Files.write(filePath, data);

            if (encryptPassword != null) {
                passwordProtectPdf(encryptPassword, filePath, filePath);
            }

            LOGGER.info("File [{}] successfuly saved", filename);

            return filePath;

        } catch (IOException e) {

            throw new SystemException("Attachment file upload failed");
        }
    }

    public boolean deleteFile(String fileName) {
        Path fullPath = baseDirectory.resolve(fileName).normalize();
        if (!fullPath.startsWith(baseDirectory)) {
            LOGGER.warn("Attempt to delete file outside baseDirectory: {}", fileName);
            return false;
        }
        try {
            return Files.deleteIfExists(baseDirectory.resolve(fileName));
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
            return false;
        }
    }

    public File getFileForDownload(Path fileName) {
        Path fullPath = baseDirectory.resolve(fileName).normalize();
        return fullPath.toFile();
    }

    private void passwordProtectPdf(String password, Path sourceFile, Path destFile) throws IOException {
        if (!Files.exists(sourceFile)) {
            throw new FileNotFoundException("File not found: " + sourceFile);
        }
        if (password == null || password.isEmpty()) {
            throw new IllegalArgumentException("Invalid password to protect file: " + sourceFile);
        }

        Path destinationFilePath = destFile != null ? destFile : sourceFile;
        boolean copySourceFile = sourceFile.equals(destinationFilePath);

        Path tmpFile = sourceFile;
        if (copySourceFile) {
            tmpFile = Paths.get(sourceFile.toString() + ".tmp");
            Files.copy(sourceFile, tmpFile, StandardCopyOption.REPLACE_EXISTING);
        }

        try (PDDocument document = PDDocument.load(tmpFile.toFile())) {
            AccessPermission accessPermission = new AccessPermission();
            StandardProtectionPolicy spp = new StandardProtectionPolicy(password, password, accessPermission);
            spp.setEncryptionKeyLength(256); // AES-256
            spp.setPermissions(accessPermission);

            document.protect(spp);
            document.save(destinationFilePath.toFile());
        } catch (Exception e) {
            throw new IOException("Failed to password protect file " + sourceFile, e);
        } finally {
            if (copySourceFile) {
                Files.deleteIfExists(tmpFile);
            }
        }
    }

}

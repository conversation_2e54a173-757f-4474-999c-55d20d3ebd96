package sk.isdd.dr.api.service;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import sk.isdd.dr.config.RecaptchaProperties;

@Service
public class RecaptchaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecaptchaService.class);

    private final RecaptchaProperties recaptchaProperties;
    private final RestTemplate restTemplate;

    public RecaptchaService(RecaptchaProperties recaptchaProperties, RestTemplate restTemplate) {
        this.recaptchaProperties = recaptchaProperties;
        this.restTemplate = restTemplate;
    }

    public boolean validateRecaptcha(String token) {
        if (!recaptchaProperties.isEnabled()) {
            LOGGER.debug("reCAPTCHA validation is disabled");
            return true;
        }

        if (token == null || token.trim().isEmpty()) {
            LOGGER.warn("reCAPTCHA token is null or empty");
            return false;
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("secret", recaptchaProperties.getSecretKey());
            params.add("response", token);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    recaptchaProperties.getVerifyUrl(),
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            if (response.getBody() != null) {
                Boolean success = (Boolean) response.getBody().get("success");
                Double score = (Double) response.getBody().get("score");
                
                LOGGER.debug("reCAPTCHA validation result: success={}, score={}", success, score);
                
                // For reCAPTCHA v3, check both success and score against threshold
                return Boolean.TRUE.equals(success) && (score == null || score >= recaptchaProperties.getScoreThreshold());
            }

            LOGGER.warn("reCAPTCHA validation failed: empty response body");
            return false;

        } catch (Exception e) {
            LOGGER.error("Error validating reCAPTCHA token", e);
            return false;
        }
    }
}

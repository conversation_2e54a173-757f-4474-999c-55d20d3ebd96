package sk.isdd.dr.api.service.api;

import org.springframework.core.io.Resource;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.request.FilterSubjectsRequest;
import sk.isdd.dr.api.dto.response.FilterSubjectsResponse;
import sk.isdd.dr.jpa.entity.SubjectEntity;

public interface SubjectService {

    SubjectEntity loadSubject(Integer subjectId);

    SubjectEntity loadSubjectByEmail(String subjectEmail);

    SubjectEntity loadSubjectByUserId(String userId);

    Subject getSubject(Integer subjectId);

    FilterSubjectsResponse filterSubjects(FilterSubjectsRequest request);

    Subject createSubject(Subject subject);

    Subject updateSubject(Subject subject);

    Subject deleteSubject(Integer subjectId);

    void deleteDeclinedSubjects();

    Subject verifySubject(Integer subjectId, boolean verified);

    Subject requestCoach(Integer subjectId);

    Subject assignCoach(Integer subjectId, String coachId);

    Resource downloadAttachment(Integer subjectId, Integer attachmentId);

    Subject approveSHLReports(Integer subjectId);

    Resource exportSubjects();

}

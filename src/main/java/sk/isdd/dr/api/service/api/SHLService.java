package sk.isdd.dr.api.service.api;

import org.springframework.web.multipart.MultipartFile;

import com.cebtalentcentral.AcknowledgeCandidateRegistration;

import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.shl.ConfirmCandidateReports;
import sk.isdd.dr.api.dto.shl.ConfirmCandidateScores;
import sk.isdd.dr.api.dto.shl.NotifyCandidateReports;
import sk.isdd.dr.api.dto.shl.NotifyCandidateScores;

public interface SHLService {

    AcknowledgeCandidateRegistration registerCandidate(Subject subject, boolean verify, String callbackHash);

    ConfirmCandidateReports processReportsPushback(Integer subjectId, String hash, NotifyCandidateReports request);

    ConfirmCandidateScores processScoresPushback(Integer subjectId, String hash, NotifyCandidateScores request);
    
    void uploadFinalReport(String subjectEmail, MultipartFile reportFile);

    void processTestCompleteCallback(Integer subjectId, String hash);

    void processTestNotCompleteCallback(Integer subjectId, String hash);

}

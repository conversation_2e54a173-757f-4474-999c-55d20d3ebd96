package sk.isdd.dr.api.service;

import java.math.BigInteger;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.UUID;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.cebtalentcentral.AcknowledgeCandidateRegistration;
import com.cebtalentcentral.ApplicationArea;
import com.cebtalentcentral.Authentication;
import com.cebtalentcentral.AuthenticationParameter;
import com.cebtalentcentral.CandidateOrderStatusDataArea.CandidateStatus.SubjectDetails;
import com.cebtalentcentral.CandidateOrderStatusDataArea.CandidateStatus.SubjectDetails.Communication;
import com.cebtalentcentral.CandidateOrderStatusDataArea.CandidateStatus.SubjectDetails.PersonName;
import com.cebtalentcentral.CandidateReportsDataArea;
import com.cebtalentcentral.Confirm;
import com.cebtalentcentral.DefaultCountry;
import com.cebtalentcentral.ITMSService;
import com.cebtalentcentral.ProcessCandidateRegistration2;
import com.cebtalentcentral.ProcessCandidateRegistration2.DataArea.CandidateRegistration;
import com.cebtalentcentral.ProcessCandidateRegistration2.DataArea.CandidateRegistration.AssessmentOrder.PackageTag;
import com.cebtalentcentral.ProcessCandidateRegistration2.DataArea.CandidateRegistration.AssessmentOrder.PushbackUrl;
import com.cebtalentcentral.ProcessCandidateRegistration2.DataArea.CandidateRegistration.AssessmentOrder.RedirectUrl;
import com.cebtalentcentral.ProjectDeadline;
import com.cebtalentcentral.ReportMedia;
import com.cebtalentcentral.Score;
import com.cebtalentcentral.StandardCountryCode;
import com.cebtalentcentral.TMSService;

import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.shl.ConfirmCandidateReports;
import sk.isdd.dr.api.dto.shl.ConfirmCandidateScores;
import sk.isdd.dr.api.dto.shl.NotifyCandidateReports;
import sk.isdd.dr.api.dto.shl.NotifyCandidateScores;
import sk.isdd.dr.api.exception.business.BadRequestException;
import sk.isdd.dr.api.exception.business.NotFoundException;
import sk.isdd.dr.api.exception.system.SystemException;
import sk.isdd.dr.api.service.api.AttachmentService;
import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.api.service.api.SHLService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.jpa.entity.AttachmentEntity;
import sk.isdd.dr.jpa.entity.ScoreEntity;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.entity.TestEntity;
import sk.isdd.dr.jpa.repository.ScoreRepository;
import sk.isdd.dr.jpa.repository.TestRepository;
import sk.isdd.dr.shl.ShlService;

@Service
public class SHLServiceImpl implements SHLService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShlService.class);

    @Autowired
    private LogService auditLogger;

    @Autowired
    private SubjectService subjectService;

    @Autowired
    private AttachmentService attachmentService;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private ScoreRepository scoreRepository;

    @Value("${shl.url}")
    private String shlUrl;

    @Value("${shl.process-candidate-registration.username}")
    private String shlUsername;

    @Value("${shl.process-candidate-registration.password}")
    private String shlPassword;

    @Value("${shl.process-candidate-registration.party-id}")
    private String shlPartyId;

    @Value("${shl.process-candidate-registration.integration-config-id}")
    private String shlIntegrationConfigId;

    @Value("${shl.process-candidate-registration.partner-name}")
    private String shlPartnerName;

    @Value("${shl.process-candidate-registration.deadline-days}")
    private Integer deadlineDays;

    @Value("${shl.process-candidate-registration.pushback-host}")
    private String pushbackHost;

    @Override
    public AcknowledgeCandidateRegistration registerCandidate(Subject subject, boolean verify, String callbackHash) {
        try {
            TMSService tmsService = new TMSService(new URL(shlUrl));
            ITMSService itmsService = tmsService.getBasicHttpBindingITMSService();
            AcknowledgeCandidateRegistration response = itmsService
                    .processCandidateRegistration(buildRegistrationRequest(subject, verify, callbackHash));
            return response;
        } catch (Exception e) {
            throw new SystemException("SHL registration failed");
        }
    }

    @Override
    public ConfirmCandidateReports processReportsPushback(Integer subjectId, String hash,
            NotifyCandidateReports request) {
        LOGGER.info("Processing SHL Pushback [Reports] for subject ID [{}], hash: [{}]", subjectId, hash);

        SubjectEntity subject = subjectService.loadSubject(subjectId);

        if (subject.getTest() != null && hash.equals(subject.getTest().getShlCallbackHash())
                && subject.getTest().getShlTestedAt() != null) {

            List<AttachmentEntity> reports = subject.getAttachments().stream()
                    .filter(a -> a.getType() == AttachmentType.FULL1 || a.getType() == AttachmentType.FULL2).toList();
            boolean hasReports = (subject.getTest().isShlTestVerify() && reports.size() == 2)
                    || (!subject.getTest().isShlTestVerify() && reports.size() == 1);

            if (!hasReports) {
                for (CandidateReportsDataArea.CandidateReports.Report report : request.getDataArea().getReports()
                        .getReport()) {

                    byte[] reportBinary = report.getReportAccess().getReportBinary();

                    if (reportBinary.length == 0) {
                        throw new BadRequestException("Empty SHL Report");
                    }

                    if (report.getPackageTag().equals("A_VIT_GPlusReport")) {
                        // verify
                        attachmentService.uploadAttachment(subject, AttachmentType.FULL2,
                                UUID.randomUUID().toString() + ".pdf",
                                "application/pdf", reportBinary);
                    } else if (report.getPackageTag().equals("P_OPQ_Profile")) {
                        // opq
                        attachmentService.uploadAttachment(subject, AttachmentType.FULL1,
                                UUID.randomUUID().toString() + ".pdf",
                                "application/pdf", reportBinary);
                    } else {
                        LOGGER.error("Invalid SHL Report type [{}]", report.getPackageTag());
                    }
                }

                LOGGER.info("SHL Reports saved for subject ID [{}]", subjectId);
            }

        } else {
            LOGGER.error("Test state of subject ID [{}] is wrong for SHL pushback", subjectId);
        }

        ConfirmCandidateReports response = new ConfirmCandidateReports();

        ConfirmCandidateReports.ApplicationArea applicationArea = new ConfirmCandidateReports.ApplicationArea();
        applicationArea.setCreationDateTime(request.getApplicationArea().getCreationDateTime());

        Confirm confirm = new Confirm();
        confirm.setStatus("Completed");
        confirm.setRejectCode(0);
        confirm.setRejectReason("");

        response.setApplicationArea(applicationArea);
        response.setConfirm(confirm);

        return response;
    }

    @Override
    public ConfirmCandidateScores processScoresPushback(Integer subjectId, String hash, NotifyCandidateScores request) {
        LOGGER.info("Processing SHL Pushback [Scores] for subject ID [{}], hash: [{}]", subjectId, hash);

        SubjectEntity subject = subjectService.loadSubject(subjectId);

        LocalDateTime now = LocalDateTime.now();

        if (subject.getTest() != null && hash.equals(subject.getTest().getShlCallbackHash())
                && subject.getTest().getShlTestedAt() != null) {

            boolean hasScores = !scoreRepository.findBySubject(subject).isEmpty();

            if (!hasScores) {
                for (Score score : request.getDataArea().getScores().getScore()) {

                    ScoreEntity scoreEntity = new ScoreEntity();
                    scoreEntity.setName(score.getScaleTag());
                    scoreEntity.setScore(Double.parseDouble(score.getValue()));
                    scoreEntity.setType(score.getScoreType());
                    scoreEntity.setCreatedAt(now);
                    scoreEntity.setFlags(0);
                    scoreEntity.setModifiedAt(now);
                    scoreEntity.setSubject(subject);

                    scoreRepository.save(scoreEntity);
                }
                LOGGER.info("SHL Scores saved for subject ID [{}]", subjectId);
            }

        } else {
            LOGGER.error("Test state of subject ID [{}] is wrong for SHL pushback", subjectId);
        }

        ConfirmCandidateScores response = new ConfirmCandidateScores();

        ConfirmCandidateScores.ApplicationArea applicationArea = new ConfirmCandidateScores.ApplicationArea();
        applicationArea.setCreationDateTime(request.getApplicationArea().getCreationDateTime());

        Confirm confirm = new Confirm();
        confirm.setStatus("Completed");
        confirm.setRejectCode(0);
        confirm.setRejectReason("");

        response.setApplicationArea(applicationArea);
        response.setConfirm(confirm);

        return response;
    }

    @Override
    public void uploadFinalReport(String subjectEmail, MultipartFile reportFile) {
        LOGGER.info("Uploading SHL final report for subject with email [{}]", subjectEmail);
        SubjectEntity subject = subjectService.loadSubjectByEmail(subjectEmail);
        if (subject.getTest() == null) {
            LOGGER.error("Subject with email [{}] is in bad state to import SHL final report", subjectEmail);
            throw new NotFoundException();
        }
        attachmentService.uploadAttachment(subject, AttachmentType.SIMPLE, reportFile);
        attachmentService.uploadAttachment(subject, AttachmentType.SIMPLE_ENCRYPTED, reportFile);
        LOGGER.info("SHL final report for subject with email [{}] uploaded successfully", subjectEmail);
    }

    @Override
    public void processTestCompleteCallback(Integer subjectId, String hash) {
        LOGGER.info("Processing complete callback of SHL test for subject ID [{}]", subjectId);
        SubjectEntity subject = subjectService.loadSubject(subjectId);
        if (subject.getTest() != null && hash.equals(subject.getTest().getShlCallbackHash())
                && subject.getTest().getShlTestedAt() == null) {
            auditLogger.log(null, subject, LogAction.SHL_TEST_COMPLETE);

            TestEntity test = subject.getTest();
            test.setShlTestedAt(LocalDateTime.now());
            testRepository.save(test);
        }
    }

    @Override
    public void processTestNotCompleteCallback(Integer subjectId, String hash) {
        LOGGER.info("Processing NOT complete callback of SHL test for subject ID [{}]", subjectId);
        SubjectEntity subject = subjectService.loadSubject(subjectId);
        if (subject.getTest() != null && hash.equals(subject.getTest().getShlCallbackHash())
                && subject.getTest().getShlTestedAt() == null) {
            auditLogger.log(null, subject, LogAction.SHL_TEST_NOT_COMPLETE);
        }
    }

    private ProcessCandidateRegistration2 buildRegistrationRequest(Subject subject, boolean verify,
            String callbackHash) {
        // pushback URIs
        String reportsPushbackUri = String.format(
                "%s/api/shl/pushback/reports/%s/%s", pushbackHost, subject.getId(),
                callbackHash);
        String scoresPushbackUri = String.format(
                "%s/api/shl/pushback/scores/%s/%s", pushbackHost, subject.getId(),
                callbackHash);

        // callback URIs
        String completedCallbackUri = String.format(
                "%s/api/shl/callback/complete/%s/%s", pushbackHost, subject.getId(),
                callbackHash);
        String notCompletedCallbackUri = String.format(
                "%s/api/shl/callback/not-complete/%s/%s", pushbackHost,
                subject.getId(),
                callbackHash);

        ProcessCandidateRegistration2 pcr = new ProcessCandidateRegistration2();
        pcr.setApplicationArea(getApplicationArea());
        com.cebtalentcentral.ProcessCandidateRegistration2.DataArea dataArea = new com.cebtalentcentral.ProcessCandidateRegistration2.DataArea();
        pcr.setDataArea(dataArea);

        CandidateRegistration candidateRegistration = new CandidateRegistration();
        candidateRegistration.setReceiptId(0);

        com.cebtalentcentral.CandidateScoresDataArea.CustomerParty customerParty = new com.cebtalentcentral.CandidateScoresDataArea.CustomerParty();
        customerParty.setPartyId(shlPartyId);

        candidateRegistration.setCustomerParty(customerParty);
        candidateRegistration.setIntegrationConfigurationId(shlIntegrationConfigId);
        candidateRegistration.setPartnerSystem(shlPartnerName);

        SubjectDetails subjectDetails = new SubjectDetails();
        subjectDetails.setSubjectId(subject.getId().toString());

        PersonName personName = new PersonName();
        personName.setGivenName(subject.getName());
        personName.setFamilyName(subject.getSurname());

        subjectDetails.setPersonName(personName);

        Communication communication = new Communication();
        communication.setLanguage("sk");
        communication.setEmail(subject.getEmail());

        subjectDetails.setCommunication(communication);

        DefaultCountry defaultCountry = new DefaultCountry();
        defaultCountry.setCountryCode(StandardCountryCode.SK);

        subjectDetails.setDefaultCountry(defaultCountry);

        candidateRegistration.setSubjectDetails(subjectDetails);

        ProcessCandidateRegistration2.DataArea.CandidateRegistration.AssessmentOrder assessmentOrder = new ProcessCandidateRegistration2.DataArea.CandidateRegistration.AssessmentOrder();
        ProjectDeadline projectDeadline = new ProjectDeadline();
        projectDeadline.setDeadlineDays(BigInteger.valueOf(deadlineDays));

        assessmentOrder.setProjectDeadline(projectDeadline);

        PackageTag packageTag = new PackageTag();
        packageTag.setReportMedia(ReportMedia.EMBEDDED);
        packageTag.setStage(BigInteger.valueOf(1));
        packageTag.setSequence(BigInteger.valueOf(1));

        if (verify) {
            packageTag.setValue("OPQ and Verify Interactive - G+");
        } else {
            packageTag.setValue("OPQ32r");
        }

        assessmentOrder.getPackageTag().add(packageTag);

        PushbackUrl pushbackUrlReports = new PushbackUrl();
        PushbackUrl pushbackUrlScores = new PushbackUrl();

        pushbackUrlReports.setSchemeId("Reports");
        pushbackUrlScores.setSchemeId("Scores");

        pushbackUrlReports.setValue(reportsPushbackUri);
        pushbackUrlScores.setValue(scoresPushbackUri);

        assessmentOrder.getPushbackUrl().add(pushbackUrlScores);
        assessmentOrder.getPushbackUrl().add(pushbackUrlReports);

        RedirectUrl completedRedirectUrl = new RedirectUrl();
        completedRedirectUrl.setSchemeId("Completed");
        completedRedirectUrl.setValue(completedCallbackUri);

        RedirectUrl notCompletedRedirectUrl = new RedirectUrl();
        notCompletedRedirectUrl.setSchemeId("NotCompleted");
        notCompletedRedirectUrl.setValue(notCompletedCallbackUri);

        assessmentOrder.getRedirectUrl().add(completedRedirectUrl);
        assessmentOrder.getRedirectUrl().add(notCompletedRedirectUrl);

        candidateRegistration.setAssessmentOrder(assessmentOrder);

        dataArea.setCandidateRegistration(candidateRegistration);

        return pcr;
    }

    private ApplicationArea getApplicationArea() {
        ApplicationArea applicationArea = new ApplicationArea();

        applicationArea.setCreationDateTime(toXmlGregorianCalendar(LocalDateTime.now()));

        Authentication authentication = new Authentication();
        authentication.setMethod("UsernameAndPassword");

        AuthenticationParameter usernameParam = new AuthenticationParameter();
        usernameParam.setName("username");
        usernameParam.setValue(shlUsername);

        AuthenticationParameter passwordParam = new AuthenticationParameter();
        passwordParam.setName("password");
        passwordParam.setValue(shlPassword);

        authentication.getAuthenticationParameter().add(usernameParam);
        authentication.getAuthenticationParameter().add(passwordParam);

        applicationArea.setAuthentication(authentication);

        return applicationArea;
    }

    private XMLGregorianCalendar toXmlGregorianCalendar(LocalDateTime localDateTime) {
        try {
            ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneOffset.UTC);
            GregorianCalendar calendar = GregorianCalendar.from(zonedDateTime);
            XMLGregorianCalendar xmlCalendar = DatatypeFactory.newInstance().newXMLGregorianCalendar(calendar);
            xmlCalendar.setTimezone(0);
            return xmlCalendar;
        } catch (Exception e) {
            throw new RuntimeException("Chyba pri konverzii na XMLGregorianCalendar", e);
        }
    }

}

package sk.isdd.dr.api.service;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import sk.isdd.dr.api.exception.system.SystemException;
import sk.isdd.dr.auth.CurrentUser;

public class BaseService {

    public CurrentUser getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            throw new SystemException("Authentication principal is not defined");
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof CurrentUser) {
            return (CurrentUser) principal;
        }

        throw new SystemException("Authentication principal is not defined");
    }


}

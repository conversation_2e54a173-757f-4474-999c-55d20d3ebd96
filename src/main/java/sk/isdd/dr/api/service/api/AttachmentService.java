package sk.isdd.dr.api.service.api;

import java.io.File;

import org.springframework.web.multipart.MultipartFile;

import sk.isdd.dr.api.dto.Attachment;
import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.jpa.entity.SubjectEntity;

public interface AttachmentService {

    Attachment uploadAttachment(SubjectEntity subject, AttachmentType type, String fileName, String contentType, byte[] fileBytes);

    Attachment uploadAttachment(SubjectEntity subject, AttachmentType type, MultipartFile file);

    Attachment getAttachment(Integer subjectId, Integer attachmentId);

    File getAttachmentFile(Integer subjectId, Integer attachmentId);

    File getAttachmentFile(Integer subjectId, AttachmentType type);

    void deleteAttachment(SubjectEntity subject, AttachmentType type);

}

package sk.isdd.dr.api.service.api;

import java.time.LocalDateTime;

import sk.isdd.dr.api.dto.CabinnetTest;
import sk.isdd.dr.api.dto.request.StoreCabinnetResultRequest;
import sk.isdd.dr.api.dto.response.GetCabinnetTestParticipantsResponse;
import sk.isdd.dr.api.dto.response.StoreCabinnetResultResponse;

public interface TestService {

    StoreCabinnetResultResponse storeCabinnetTestResult(Integer subjectId, StoreCabinnetResultRequest request);

    GetCabinnetTestParticipantsResponse getCabinnetTestParticipants(LocalDateTime timeFrom);

    CabinnetTest getCabinnetTestResults(String subjectEmail);

}

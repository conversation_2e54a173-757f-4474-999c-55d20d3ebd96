package sk.isdd.dr.api.service;

import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import sk.isdd.dr.api.dto.Consents;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.User;
import sk.isdd.dr.api.dto.request.RegisterRequest;
import sk.isdd.dr.api.exception.business.BadRequestException;
import sk.isdd.dr.api.mapper.MainMapper;
import sk.isdd.dr.api.service.api.AuthService;
import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.auth.UserManager;
import sk.isdd.dr.common.enums.LogAction;

@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private SubjectService subjectService;

    @Autowired
    private MainMapper mapper;

    @Autowired
    private UserManager userManager;

    @Autowired
    private LogService auditLogger;

    @Override
    @Transactional
    public User register(RegisterRequest request, Boolean retry) {
        if (retry != null && retry.booleanValue()) {
            userManager.createClient(request.getEmail(), null, true);
            return new User();
        }

        if (!request.getConsents().isProcessingOfPersonalData()) {
            // nesuhlas so spracovanim os. udajov
            throw new BadRequestException();
        }

        UserRepresentation user = userManager.createClient(request.getEmail(), null, false);

        Subject subject = new Subject();
        subject.setEmail(user.getEmail());
        subject.setUserId(user.getId());

        Consents consents = new Consents();
        consents.setProcessingOfPersonalData(request.getConsents().isProcessingOfPersonalData());
        consents.setMarketing(request.getConsents().isMarketing());
        subject.setConsents(consents);

        Subject createdSubject = subjectService.createSubject(subject);

        if (createdSubject != null) {
            auditLogger.log(user.getId(), subjectService.loadSubject(createdSubject.getId()), LogAction.REGISTER);
            return mapper.mapUserRepresentationToUser(user);
        }

        return new User();
    }

}

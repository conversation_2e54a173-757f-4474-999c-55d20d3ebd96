package sk.isdd.dr.api.service;

import java.io.ByteArrayOutputStream;
import java.io.OutputStreamWriter;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import com.opencsv.CSVWriter;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;

import org.springframework.util.StringUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import sk.isdd.dr.api.dto.Attachment;
import sk.isdd.dr.api.dto.Consents;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.csv.ExportedSubject;
import sk.isdd.dr.api.dto.request.FilterSubjectsRequest;
import sk.isdd.dr.api.dto.response.FilterSubjectsResponse;
import sk.isdd.dr.api.dto.response.GetSubjectAttachmentsResponse;
import sk.isdd.dr.api.exception.business.BadRequestException;
import sk.isdd.dr.api.exception.business.BadSubjectStateException;
import sk.isdd.dr.api.exception.business.NotFoundException;
import sk.isdd.dr.api.exception.system.SystemException;
import sk.isdd.dr.api.mapper.MainMapper;
import sk.isdd.dr.api.service.api.AttachmentService;
import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.auth.UserManager;
import sk.isdd.dr.auth.UserRole;
import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.common.util.BirthNumber;
import sk.isdd.dr.email.MailchimpSenderService;
import sk.isdd.dr.jpa.entity.AttachmentEntity;
import sk.isdd.dr.jpa.entity.PartnerEntity;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.entity.UserEntity;
import sk.isdd.dr.jpa.repository.PartnerRepository;
import sk.isdd.dr.jpa.repository.SubjectRepository;

@Service
public class SubjectServiceImpl extends BaseService implements SubjectService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubjectServiceImpl.class);

    @Autowired
    private MailchimpSenderService emailSender;

    @Autowired
    private UserManager userManager;

    private SubjectRepository subjectRepository;

    @Autowired
    private PartnerRepository partnerRepository;

    private AttachmentService attachmentService;

    @Autowired
    private LogService auditLogger;

    private MainMapper mapper;

    @PersistenceContext
    private EntityManager entityManager;

    public SubjectServiceImpl(SubjectRepository subjectRepository,
            AttachmentService attachmentService,
            MainMapper mapper) {
        this.subjectRepository = subjectRepository;
        this.attachmentService = attachmentService;
        this.mapper = mapper;
    }

    @Override
    @Transactional
    public Subject createSubject(Subject subject) {

        SubjectEntity subjectEntity = subjectRepository.findByEmail(subject.getEmail()).orElse(null);

        if (subjectEntity != null) {
            return null;
        }

        subjectEntity = new SubjectEntity();

        subjectEntity.setName(subject.getName());
        subjectEntity.setSurname(subject.getSurname());
        subjectEntity.setCreatedAt(LocalDateTime.now());
        subjectEntity.setIdNumber(subject.getIdNumber());
        subjectEntity.setEmail(subject.getEmail());
        subjectEntity.setPhoneNumber(subject.getPhoneNumber());
        subjectEntity.setUserId(subject.getUserId());
        subjectEntity.setFlags(0);
        subjectEntity.setType("person");

        if (subject.getPartner() != null && subject.getPartner().getId() != null) {
            subjectEntity.setPartner(partnerRepository.findById(subject.getPartner().getId())
                    .orElseThrow(() -> new SystemException("Partner not found")));
        }

        subjectEntity.setConsents(mapper.consentsDTOToInt(subject.getConsents()));

        subjectRepository.save(subjectEntity);

        return mapper.mapSubjectEntity(subjectEntity);
    }

    @Override
    @Transactional
    public Subject updateSubject(Subject subject) {
        if (subject == null) {
            throw new BadRequestException();
        }

        SubjectEntity subjectEntity = loadSubject(subject.getId());

        boolean modified = false;

        if (StringUtils.hasText(subject.getName()) && !StringUtils.hasText(subjectEntity.getName())) {
            // not allowed to change if defined
            subjectEntity.setName(subject.getName());
            modified = true;
        }

        if (StringUtils.hasText(subject.getSurname()) && !StringUtils.hasText(subjectEntity.getSurname())) {
            // not allowed to change if defined
            subjectEntity.setSurname(subject.getSurname());
            modified = true;
        }

        if (StringUtils.hasText(subject.getIdNumber()) && !StringUtils.hasText(subjectEntity.getIdNumber())) {
            // not allowed to change if defined
            BirthNumber birthNumber = new BirthNumber(subject.getIdNumber());
            subjectEntity.setIdNumber(birthNumber.getCleanedNumber());
            modified = true;
        }

        if (subjectEntity.getPhoneNumber() == null
                || !subjectEntity.getPhoneNumber().equals(subject.getPhoneNumber())) {
            subjectEntity.setPhoneNumber(subject.getPhoneNumber());
            modified = true;
        }

        if (subjectEntity.getLinkedInProfile() == null
                || !subjectEntity.getLinkedInProfile().equals(subject.getLinkedInProfile())) {
            subjectEntity.setLinkedInProfile(subject.getLinkedInProfile());
            modified = true;
        }

        if (subject.getConsents() != null) {
            subjectEntity.setConsents(mapper.consentsDTOToInt(subject.getConsents()));
            modified = true;
        }

        if (modified) {
            subjectEntity.setModifiedAt(LocalDateTime.now());
            auditLogger.log(null, subjectEntity, LogAction.SUBJECT_UPDATED);
            subjectRepository.save(subjectEntity);
        }

        return mapper.mapSubjectEntity(subjectEntity);
    }

    @Override
    public Subject getSubject(Integer subjectId) {
        SubjectEntity subjectEntity = loadSubject(subjectId);
        Subject subject = mapper.mapSubjectEntity(subjectEntity);

        if (getCurrentUser().isClient() && subjectEntity.getShlReportsApprovedAt() == null) {
            subject.setAttachments(subject.getAttachments().stream()
                    .filter(attachment -> attachment.getType() != AttachmentType.SIMPLE_ENCRYPTED
                            && attachment.getType() != AttachmentType.SIMPLE
                            && attachment.getType() != AttachmentType.FULL1
                            && attachment.getType() != AttachmentType.FULL2)
                    .toList());
        }

        return subject;
    }

    @Override
    @Transactional
    public Subject deleteSubject(Integer subjectId) {
        SubjectEntity subject = loadSubject(subjectId);
        return deleteSubject(subject);
    }

    @Override
    @Transactional
    public void deleteDeclinedSubjects() {
        List<SubjectEntity> declinedSubjects = subjectRepository
                .getDeclinedSubjects(LocalDateTime.now().minusDays(120));

        if (declinedSubjects.isEmpty()) {
            LOGGER.info("No declined clients to delete");
            return;
        }

        LOGGER.info("Found [{}] declined clients to delete", declinedSubjects.size());

        declinedSubjects.forEach(subject -> {
            deleteSubject(subject.getId());
        });
    }

    @Transactional
    private Subject deleteSubject(SubjectEntity subjectEntity) {

        if (subjectEntity.getDeletedAt() != null) {
            throw new BadSubjectStateException();
        }

        Consents consents = mapper.intConsentsToDTO(subjectEntity.getConsents());

        subjectEntity.setDeletedAt(LocalDateTime.now());
        subjectEntity.setIdNumber(null);

        attachmentService.deleteAttachment(subjectEntity, AttachmentType.CV);

        if (!consents.isMarketing()) {
            subjectEntity.setName("XXX");
            subjectEntity.setSurname("XXX");
            subjectEntity.setEmail(String.format("%<EMAIL>", subjectEntity.getId()));
            subjectEntity.setPhoneNumber(null);
            subjectEntity.setLinkedInProfile(null);
        }

        auditLogger.log(null, subjectEntity, LogAction.SUBJECT_DELETED);

        Subject subject = mapper.mapSubjectEntity(subjectEntity);

        if (subjectEntity.getUserId() != null) {
            // delete also from Keycloak
            userManager.deactiveClient(subject, true);
            subjectEntity.setUserId(null);
            auditLogger.log(null, subjectEntity, LogAction.USER_DELETED);
        }

        subjectRepository.save(subjectEntity);

        LOGGER.info("Deleted subject with ID [{}]", subjectEntity.getId());

        return subject;
    }

    @Override
    public Resource downloadAttachment(Integer subjectId, Integer attachmentId) {
        SubjectEntity subject = loadSubject(subjectId);

        Attachment attachment = attachmentService.getAttachment(subjectId, attachmentId);

        if (getCurrentUser().isClient()) {
            // client can view only SHL final report (if approved by admin) and coach report
            boolean notAllowedAttachment = attachment.getType() != AttachmentType.COACH_REPORT
                    && attachment.getType() != AttachmentType.SIMPLE && attachment.getType() != AttachmentType.CV;
            boolean simpleNotApproved = attachment.getType() == AttachmentType.SIMPLE
                    && subject.getShlReportsApprovedAt() == null;
            if (notAllowedAttachment || simpleNotApproved) {
                throw new NotFoundException();
            }
        }

        if (getCurrentUser().isCoach() && !subject.getCoachId().equals(getCurrentUser().getId())) {
            // coach can view everything ONLY for his subject
            throw new NotFoundException();
        }

        if (getCurrentUser().isPartnerAdmin() && subject.getPartner().getId() != getCurrentUser().getPartnerId()) {
            // partner admin can view everyting ONLY for his employees
            throw new NotFoundException();
        }

        if (getCurrentUser().isPartnerAdmin()
                && mapper.intConsentsToDTO(subject.getConsents()).isTestReportsVisibility()
                && attachment.getType() != AttachmentType.CV) {
            // partner admin can view reports of client only if client has consent
            throw new NotFoundException();
        }

        return new FileSystemResource(attachmentService.getAttachmentFile(subjectId, attachmentId));
    }

    // @Override
    public GetSubjectAttachmentsResponse getSubjectAttachments(Integer subjectId) {
        SubjectEntity subject = loadSubject(subjectId);

        List<Attachment> attachments = subject.getAttachments()
                .stream()
                .filter(attachment -> !getCurrentUser().isClient() || subject.getShlReportsApprovedAt() != null
                        || (attachment.getType() != AttachmentType.SIMPLE
                                && attachment.getType() != AttachmentType.FULL1
                                && attachment.getType() != AttachmentType.FULL2))
                .map(a -> mapper.mapAttachmentEntity(a)).toList();

        GetSubjectAttachmentsResponse response = new GetSubjectAttachmentsResponse();
        response.setAttachments(attachments);

        return response;
    }

    @Override
    public FilterSubjectsResponse filterSubjects(FilterSubjectsRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<SubjectEntity> cq = cb.createQuery(SubjectEntity.class);
        Root<SubjectEntity> root = cq.from(SubjectEntity.class);

        List<Predicate> predicates = new ArrayList<>();

        predicates.add(cb.isNull(root.get("deletedAt")));

        if (StringUtils.hasText(request.getCoachId())) {
            predicates.add(cb.equal(root.get("coachId"), request.getCoachId()));
        }

        if (StringUtils.hasText(request.getIdNumber())) {
            predicates.add(cb.equal(root.get("idNumber"), request.getIdNumber()));
        }

        if (request.getPartnerId() != null) {
            Join<UserEntity, PartnerEntity> partnerJoin = root.join("partner");
            predicates.add(cb.equal(partnerJoin.get("id"), request.getPartnerId()));
        }

        if (request.getState() != null) {
            switch (request.getState()) {
                case PRE_REGISTERED:
                    predicates.add(cb.and(cb.isNull(root.get("verifiedAt")),
                            cb.isNull(root.get("idNumber"))));
                    break;
                case REGISTERED:
                    if (request.getPartnerId() != null) {
                        predicates.add(cb.isNotNull(root.get("idNumber")));
                    }
                    break;
                case REGISTERED_NOT_VERIFIED:
                    predicates.add(cb.and(cb.isNull(root.get("verifiedAt")), cb.isNotNull(root.get("idNumber"))));
                case REGISTERED_APPROVED:
                    predicates.add(cb.and(cb.isNotNull(root.get("verifiedAt")),
                            cb.isTrue(root.get("verified"))));
                    break;
                case REGISTERED_DECLINED:
                    predicates.add(cb.and(cb.isNotNull(root.get("verifiedAt")),
                            cb.isFalse(root.get("verified"))));
                    break;
                case COACH_REQUESTED:
                    predicates
                            .add(cb.and(cb.isNotNull(root.get("coachRequestedAt"))));
                    break;
                default:
                    break;
            }
        }

        if (StringUtils.hasText(request.getQuery())) {
            String pattern = "%" + request.getQuery().toLowerCase() + "%";
            Predicate nameLike = cb.like(cb.lower(root.get("name")), pattern);
            Predicate surnameLike = cb.like(cb.lower(root.get("surname")), pattern);
            Predicate emailLike = cb.like(cb.lower(root.get("email")), pattern);

            predicates.add(cb.or(nameLike, surnameLike, emailLike));
        }

        cq.where(predicates.toArray(new Predicate[0]));

        TypedQuery<SubjectEntity> query = entityManager.createQuery(cq);

        int count = query.getResultList().size();

        if (request.getPagination() != null) {
            int page = request.getPagination().getPage();
            int size = request.getPagination().getCount();
            query.setFirstResult(page * size);
            query.setMaxResults(size);
        }

        List<SubjectEntity> resultList = query.getResultList();

        List<Subject> subjects = resultList.stream()
                .map((s) -> mapper.mapSubjectEntity(s))
                .toList();

        FilterSubjectsResponse response = new FilterSubjectsResponse();
        response.setData(subjects);
        response.setTotalCount(count);

        return response;
    }

    @Override
    @Transactional
    public Subject requestCoach(Integer subjectId) {
        SubjectEntity subject = loadSubject(subjectId);

        if (subject.getCoachRequestedAt() != null) {
            throw new BadSubjectStateException();
        }

        if (subject.getTest() == null || subject.getTest().getShlTestedAt() == null) {
            // subject is NOT full tested
            throw new BadSubjectStateException();
        }

        subject.setCoachRequestedAt(LocalDateTime.now());
        subject.setModifiedAt(subject.getCoachRequestedAt());

        subjectRepository.save(subject);

        // send email to all coach admins
        List<UserRepresentation> coachAdmins = userManager.getUsersByRole(UserRole.COACH_ADMIN, null, null);
        coachAdmins.forEach(coachAdmin -> emailSender.sendTemplateEmail(coachAdmin.getEmail(),
                EmailTemplateCode.COACH_REQUEST_ADMIN, null, null));

        emailSender.sendTemplateEmail(subject.getEmail(), EmailTemplateCode.COACH_REQUEST, null, null);

        auditLogger.log(getCurrentUser().getId(), subject, LogAction.COACH_REQUESTED);

        return mapper.mapSubjectEntity(subject);
    }

    @Override
    @Transactional
    public Subject assignCoach(Integer subjectId, String coachId) {
        SubjectEntity subject = loadSubject(subjectId);
        UserRepresentation coach = userManager.getUserById(coachId)
                .orElseThrow(() -> new NotFoundException("Coach not found"));

        if (subject.getCoachRequestedAt() == null) {
            throw new BadSubjectStateException();
        }

        subject.setCoachId(coachId);
        subject.setModifiedAt(LocalDateTime.now());

        SubjectEntity savedSubject = subjectRepository.save(subject);

        auditLogger.log(getCurrentUser().getId(), savedSubject, LogAction.COACH_ASSIGNED,
                String.format("Assigned coach email: %s", coach.getEmail()), null);

        return mapper.mapSubjectEntity(savedSubject);
    }

    @Override
    public SubjectEntity loadSubject(Integer subjectId) {
        return subjectRepository.findById(subjectId)
                .orElseThrow(() -> new NotFoundException());
    }

    @Override
    public SubjectEntity loadSubjectByEmail(String email) {
        return subjectRepository.findByEmail(email)
                .orElseThrow(() -> new NotFoundException());
    }

    @Override
    public SubjectEntity loadSubjectByUserId(String userId) {
        return subjectRepository.findByUserId(userId)
                .orElseThrow(() -> new NotFoundException());
    }

    @Override
    @Transactional
    public Subject verifySubject(Integer subjectId, boolean verified) {
        SubjectEntity subject = loadSubject(subjectId);

        if (subject.getDeletedAt() != null) {
            throw new BadSubjectStateException();
        }

        if (subject.getVerifiedAt() != null) {
            throw new BadSubjectStateException();
        }

        subject.setVerified(verified);
        subject.setVerifiedAt(LocalDateTime.now());
        subject.setModifiedAt(subject.getVerifiedAt());

        Subject subjectDTO = mapper.mapSubjectEntity(subject);

        if (verified) {
            userManager.approveClient(subjectDTO);
        } else {
            userManager.declineClient(subjectDTO);
        }

        auditLogger.log(getCurrentUser().getId(), subject,
                verified ? LogAction.REGISTRATION_APPROVE : LogAction.REGISTRATION_DECLINE);

        emailSender.sendTemplateEmail(subject.getEmail(),
                verified ? EmailTemplateCode.REGISTRATION_APPROVED : EmailTemplateCode.REGISTRATION_DECLINED, null,
                null);

        return subjectDTO;
    }

    @Override
    @Transactional
    public Subject approveSHLReports(Integer subjectId) {
        SubjectEntity subject = loadSubject(subjectId);

        if (subject.getShlReportsApprovedAt() != null) {
            throw new BadSubjectStateException();
        }

        // find final SHL report for given subject
        AttachmentEntity attachment = subject.getAttachments().stream()
                .filter(a -> a.getType() == AttachmentType.SIMPLE_ENCRYPTED)
                .findFirst()
                .orElseThrow(() -> new BadSubjectStateException());

        subject.setShlReportsApprovedAt(LocalDateTime.now());

        subjectRepository.save(subject);

        auditLogger.log(getCurrentUser().getId(), subject, LogAction.SUBJECT_REPORTS_APPROVED);

        emailSender.sendTemplateEmail(subject.getEmail(), EmailTemplateCode.TEST_RESULTS, null,
                List.of(mapper.mapAttachmentEntity(attachment)));

        return mapper.mapSubjectEntity(subject);
    }

    public Resource exportSubjects() {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
                OutputStreamWriter writer = new OutputStreamWriter(out)) {

            List<Subject> subjects = subjectRepository.findAll().stream().map(s -> mapper.mapSubjectEntity(s)).toList();

            List<ExportedSubject> exportedSubjects = mapper.toExportedSubjects(subjects);

            ColumnPositionMappingStrategy<ExportedSubject> strategy = new ColumnPositionMappingStrategy<>();
            strategy.setType(ExportedSubject.class);

            strategy.setColumnMapping(new String[] {
                    "createdAt", "approved", "name", "surname", "idNumber", "phoneNumber", "email",
                    "hasCV", "linkedInProfile", "consent1", "consent2", "consent3", "consent4",
                    "approvedAt", "coachRequestedAt"
            });

            String header = String.join(";", List.of(
                    "Registrácia", "Schválený", "Meno", "Priezvisko", "Rod. č.", "Tel. č.", "Email",
                    "cv", "LinkedIn", "Súhlas 1", "Súhlas 2", "Súhlas 3", "Súhlas 4",
                    "Overený ISSZ", "Požiadal o koučing"));
            writer.write(header);
            writer.write("\n");

            StatefulBeanToCsv<ExportedSubject> csvWriter = new StatefulBeanToCsvBuilder<ExportedSubject>(writer)
                    .withQuotechar(CSVWriter.NO_QUOTE_CHARACTER)
                    .withSeparator(';')
                    .withOrderedResults(true)
                    .withMappingStrategy(strategy)
                    .build();

            csvWriter.write(exportedSubjects);
            writer.flush();

            return new ByteArrayResource(out.toByteArray());

        } catch (Exception e) {
            return null;
        }

    }

}

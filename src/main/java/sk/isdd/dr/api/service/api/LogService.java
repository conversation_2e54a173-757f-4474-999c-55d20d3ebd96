package sk.isdd.dr.api.service.api;

import java.util.List;
import java.util.Map;

import sk.isdd.dr.api.dto.Log;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.jpa.entity.SubjectEntity;

public interface LogService {

    void log(String userId, SubjectEntity subject, LogAction action);

    void log(String userId, SubjectEntity subject, LogAction action, String message, Map<String, Object> extraData);

    List<Log> getAuditLogs(Integer subjectId);

}

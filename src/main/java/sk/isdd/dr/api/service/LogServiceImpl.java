package sk.isdd.dr.api.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.transaction.Transactional;
import sk.isdd.dr.api.dto.Log;
import sk.isdd.dr.api.mapper.MainMapper;
import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.auth.UserManager;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.jpa.entity.LogEntity;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.repository.LogRepository;

@Service
public class LogServiceImpl implements LogService {

    @Autowired
    private UserManager userManager;

    @Autowired
    private LogRepository logRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MainMapper mapper;

    @Override
    @Transactional
    public void log(String userId, SubjectEntity subject, LogAction action) {
        log(userId, subject, action, null, null);
    }

    @Override
    @Transactional
    public void log(String userId, SubjectEntity subject, LogAction action, String message,
            Map<String, Object> extraData) {

        LogEntity log = new LogEntity();
        log.setCreatedAt(LocalDateTime.now());
        log.setUserId(userId);
        log.setSubject(subject);
        log.setAction(action);
        log.setMessage(message);

        if (extraData != null) {
            try {
                log.setExtraData(objectMapper.writeValueAsString(extraData));
            } catch (Exception e) {
                log.setExtraData("failed convert extra data: " + e.getMessage());
            }
        }

        logRepository.save(log);
    }

    @Override
    public List<Log> getAuditLogs(Integer subjectId) {
        List<LogEntity> logs = logRepository.findBySubjectId(subjectId);
        return logs.stream().map(log -> {
            Log logDTO = mapper.mapAuditLogEntity(log);
            if (log.getUserId() != null) {
                UserRepresentation user = userManager.getUserById(log.getUserId()).orElse(null);
                logDTO.setUser(user != null ? mapper.mapUserRepresentationToUser(user) : null);
            }
            return logDTO;
        }).toList();
    }

}

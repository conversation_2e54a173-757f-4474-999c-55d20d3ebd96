package sk.isdd.dr.api.service.api;

import sk.isdd.dr.api.dto.Partner;
import sk.isdd.dr.api.dto.request.InvitePartnerClientsRequest;
import sk.isdd.dr.api.dto.response.InvitePartnerClientsResponse;

public interface PartnerService {

    Partner getPartner(Integer partnerId);

    Partner updatePartner(Integer partnerId, Partner partner);

    InvitePartnerClientsResponse inviteClients(Integer partnerId, InvitePartnerClientsRequest request);

}

package sk.isdd.dr.api.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import sk.isdd.dr.api.dto.chatbot.ChatGPTRequest;
import sk.isdd.dr.api.dto.chatbot.ChatGPTResponse;
import sk.isdd.dr.api.dto.chatbot.ClaudeMessage;
import sk.isdd.dr.api.dto.chatbot.ClaudeRequest;
import sk.isdd.dr.api.dto.chatbot.ClaudeResponse;
import sk.isdd.dr.api.dto.response.GetChatbotMessageResponse;
import sk.isdd.dr.api.dto.chatbot.ChatGPTRequest.ChatMessage;
import sk.isdd.dr.api.service.api.ChatbotService;

@Service
public class ChatbotServiceImpl implements ChatbotService {

    private static final String initialQuery = "Úloha: Si AI poradca na tému budúcnosti povolaní. Poskytuješ realistické predpovede o vplyve automatizácie a umelej inteligencie na povolania v nasledujúcich 3 rokoch, "
            + "pričom berieš do úvahy regionálne podmienky a aktuálnu situáciu na trhu práce Slovensku. Na iné otázky neodpovedáš.\n"
            + "Pokyny:\n"
            + "1. Ak povolanie nie je jasne definované, odpovedz: „Zadajte prosím povolanie alebo oblasť práce.“\n"
            + "2. Na otázky mimo témy odpovedz: „Som tu na analýzu budúcnosti povolaní. Iné témy nespracovávam.“\n"
            + "3. Na povolanie, ktoré je neetické alebo nelegálne odpovedz: „Nemôžem odpovedať na otázky o neetických alebo nelegálnych povolaniach.“\n"
            + "Štruktúra odpovede:\n"
            + "1. Vplyv AI a automatizácie: Uveď odhad dopadu (Žiadny, Minimálny, Stredný, Vysoký, Kritický). Zohľadni regionálne podmienky a realistické tempo technologického pokroku.\n"
            + "2. Očakávané zmeny: Popíš 3 hlavné zmeny, ktoré sú realistické a pravdepodobné v nasledujúcich 3 rokoch. V prípade neistoty uveď opatrný odhad.\n"
            + "3. Ako sa pripraviť: Navrhni 3 praktické kroky, ktoré môžu ľudia podniknúť, aby sa adaptovali na možné zmeny, pričom sa sústreď na aktuálne dostupné zdroje a možnosti v ich regióne.\n"
            + "4. Odporúčanie: Uveď: „Odporúčam sledovať trendy AI a automatizácie pre váš úspech.“";

    @Value("${chatbot.chatgpt.key}")
    private String chatgptKey;

    @Value("${chatbot.claude.key}")
    private String claudeKey;

    @Value("${chatbot.chatgpt.url}")
    private String chatgptUrl;

    @Value("${chatbot.claude.url}")
    private String claudeUrl;

    @Value("${chatbot.chatgpt.model}")
    private String chatgptModel;

    @Value("${chatbot.claude.model}")
    private String claudeModel;

    @Override
    public GetChatbotMessageResponse getChatGPTResponse(String query) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(chatgptKey);
        headers.setContentType(MediaType.APPLICATION_JSON);

        ChatGPTRequest request = new ChatGPTRequest(
                chatgptModel,
                List.of(
                        new ChatMessage("system", initialQuery),
                        new ChatMessage("user", query)));

        HttpEntity<ChatGPTRequest> entity = new HttpEntity<>(request, headers);

        ResponseEntity<ChatGPTResponse> response = restTemplate.exchange(
                chatgptUrl,
                HttpMethod.POST,
                entity,
                ChatGPTResponse.class);

        if (response.getBody() == null) {
            return new GetChatbotMessageResponse("Žiadna odpoveď z ChatGPT.");
        }

        List<ChatGPTResponse.Choice> choices = response.getBody().getChoices();
        if (choices != null && !choices.isEmpty()) {
            return new GetChatbotMessageResponse(choices.get(0).getMessage().getContent());
        } else {
            return new GetChatbotMessageResponse("Žiadna odpoveď z ChatGPT.");
        }
    }

    @Override
    public GetChatbotMessageResponse getClaudeResponse(String query) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.set("x-api-key", claudeKey);
        headers.set("anthropic-version", "2023-06-01");
        headers.setContentType(MediaType.APPLICATION_JSON);

        ClaudeMessage userMessage = new ClaudeMessage("user", query);
        ClaudeRequest request = new ClaudeRequest(
                claudeModel,
                initialQuery,
                List.of(userMessage),
                1000);

        HttpEntity<ClaudeRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<ClaudeResponse> response = restTemplate.exchange(
                    claudeUrl,
                    HttpMethod.POST,
                    entity,
                    ClaudeResponse.class);
            if (response.getBody() != null && !response.getBody().getContent().isEmpty()
                    && response.getBody().getContent().getFirst().getText() != null) {
                return new GetChatbotMessageResponse(response.getBody().getContent().getFirst().getText());
            }
            return new GetChatbotMessageResponse("Žiadna odpoveď z Claude.");
        } catch (Exception e) {
            return new GetChatbotMessageResponse("Žiadna odpoveď z Claude.");
        }
    }

}

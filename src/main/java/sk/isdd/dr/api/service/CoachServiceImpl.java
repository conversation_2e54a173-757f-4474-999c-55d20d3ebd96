package sk.isdd.dr.api.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.transaction.Transactional;
import sk.isdd.dr.api.dto.Attachment;
import sk.isdd.dr.api.exception.business.NotFoundException;
import sk.isdd.dr.api.service.api.AttachmentService;
import sk.isdd.dr.api.service.api.CoachService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.auth.CurrentUser;
import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.email.MailchimpSenderService;
import sk.isdd.dr.jpa.entity.SubjectEntity;

@Service
public class CoachServiceImpl extends BaseService implements CoachService {

    @Autowired
    private MailchimpSenderService mailSender;

    @Autowired
    private SubjectService subjectService;

    private final AttachmentService attachmentService;

    public CoachServiceImpl(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    @Override
    @Transactional
    public Attachment uploadCoachReport(Integer subjectId, MultipartFile reportFile) {
        CurrentUser currentUser = getCurrentUser();

        SubjectEntity subject = subjectService.loadSubject(subjectId);

        if (subject.getDeletedAt() != null || subject.getCoachId() == null
                || (currentUser.isCoach() && !subject.getCoachId().equals(currentUser.getId()))) {
            throw new NotFoundException();
        }

        Attachment attachment = attachmentService.uploadAttachment(subject, AttachmentType.COACH_REPORT, reportFile);
        attachmentService.uploadAttachment(subject, AttachmentType.COACH_REPORT_ENCRYPTED, reportFile);

        mailSender.sendTemplateEmail(subject.getEmail(), EmailTemplateCode.COACH_REPORT, null, null);

        return attachment;
    }

}

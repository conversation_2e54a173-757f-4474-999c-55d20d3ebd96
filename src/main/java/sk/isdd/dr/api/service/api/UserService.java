package sk.isdd.dr.api.service.api;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import sk.isdd.dr.api.dto.User;
import sk.isdd.dr.api.dto.request.UpdateUserPasswordRequest;
import sk.isdd.dr.api.dto.request.UpdateUserDataRequest;

public interface UserService {

    User updateUser(UpdateUserDataRequest request, MultipartFile cvFile);

    User updateUserPassword(UpdateUserPasswordRequest request);

    List<User> getCoaches();

}

package sk.isdd.dr.api.service;

import java.io.File;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.transaction.Transactional;
import sk.isdd.dr.api.dto.Attachment;
import sk.isdd.dr.api.exception.business.NotFoundException;
import sk.isdd.dr.api.exception.system.SystemException;
import sk.isdd.dr.api.mapper.MainMapper;
import sk.isdd.dr.api.service.api.AttachmentService;
import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.file.AttachmentFileManager;
import sk.isdd.dr.jpa.entity.AttachmentEntity;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.repository.AttachmentRepository;

@Service
public class AttachmentServiceImpl extends BaseService implements AttachmentService {

    private final DateTimeFormatter attachmentFolderNameFormatter = DateTimeFormatter.ofPattern("yyyy-LL");
    private final DateTimeFormatter attachmentFileNameFormatter = DateTimeFormatter.ofPattern("hhmmss");

    @Autowired
    private AttachmentRepository attachmentRepository;

    @Autowired
    private AttachmentFileManager attachmentFileManager;

    @Autowired
    private LogService auditLogger;

    @Autowired
    private MainMapper mapper;

    @Override
    public Attachment uploadAttachment(SubjectEntity subject, AttachmentType type, String fileName, String contentType, byte[] fileBytes) {
        AttachmentEntity attachment = attachmentRepository.findBySubjectAndType(subject, type).orElse(null);

        if (attachment != null) {
            attachmentFileManager.deleteFile(resolveAttachmentFileName(attachment));
            attachmentRepository.delete(attachment);
            auditLogger.log(null, subject, LogAction.ATTACHMENT_DELETED);
        }

        attachment = new AttachmentEntity();
        attachment.setCreatedAt(LocalDateTime.now());
        attachment.setFileName(fileName);
        attachment.setMimeType(contentType);
        attachment.setSubject(subject);
        attachment.setType(type);
        attachment.setFlags(0);

        attachmentRepository.save(attachment);

        String encryptPassword = attachment.getType() == AttachmentType.COACH_REPORT_ENCRYPTED
                || attachment.getType() == AttachmentType.SIMPLE_ENCRYPTED ? attachment.getSubject().getIdNumber()
                        : null;

        attachmentFileManager.saveFile(resolveAttachmentFileName(attachment), fileBytes, encryptPassword);

        auditLogger.log(null, subject, LogAction.ATTACHMENT_ADDED);

        return mapper.mapAttachmentEntity(attachment);
    }

    @Override
    @Transactional
    public Attachment uploadAttachment(SubjectEntity subject, AttachmentType type, MultipartFile file) {
        try {
            return uploadAttachment(subject, type, file.getOriginalFilename(), file.getContentType(), file.getBytes());
        } catch (Exception e) {
            throw new SystemException("System Exception");
        }
    }

    @Override
    public Attachment getAttachment(Integer subjectId, Integer attachmentId) {
        return mapper.mapAttachmentEntity(loadAttachment(subjectId, attachmentId));
    }

    @Override
    public File getAttachmentFile(Integer subjectId, AttachmentType type) {
        AttachmentEntity attachment = loadAttachment(subjectId, type);
        String attachmentFileName = resolveAttachmentFileName(attachment);
        return attachmentFileManager.getFileForDownload(Path.of(attachmentFileName));
    }

    @Override
    public File getAttachmentFile(Integer subjectId, Integer attachmentId) {
        AttachmentEntity attachment = loadAttachment(subjectId, attachmentId);
        String attachmentFileName = resolveAttachmentFileName(attachment);
        return attachmentFileManager.getFileForDownload(Path.of(attachmentFileName));
    }

    @Override
    public void deleteAttachment(SubjectEntity subject, AttachmentType type) {
        AttachmentEntity attachment = attachmentRepository.findBySubjectAndType(subject, type).orElse(null);

        if (attachment != null) {
            attachmentFileManager.deleteFile(resolveAttachmentFileName(attachment));
            attachmentRepository.delete(attachment);
            auditLogger.log(null, subject, LogAction.ATTACHMENT_DELETED);
        }
    }

    private String resolveAttachmentFileName(AttachmentEntity attachment) {
        String folderName = attachment.getSubject().getCreatedAt().format(attachmentFolderNameFormatter);
        String fileName = attachment.getSubject().getCreatedAt().format(attachmentFileNameFormatter);

        if (attachment.getType() == AttachmentType.CV) {
            return String.format("%s/%s-%s.%s", folderName, attachment.getSubject().getId(), fileName,
                    getFileExt(attachment.getFileName()));
        }
        return String.format("%s/%s-%s-%s.%s", folderName, attachment.getSubject().getId(), fileName,
                attachment.getType().name().toLowerCase(), getFileExt(attachment.getFileName()));
    }

    private String getFileExt(String filename) {
        return filename.substring(filename.lastIndexOf('.') + 1);
    }

    private AttachmentEntity loadAttachment(Integer subjectId, Integer attachmentId) {
        return attachmentRepository.findBySubjectIdAndId(subjectId, attachmentId)
                .orElseThrow(() -> new NotFoundException());
    }

    private AttachmentEntity loadAttachment(Integer subjectId, AttachmentType type) {
        return attachmentRepository.findBySubjectIdAndType(subjectId, type)
                .orElseThrow(() -> new NotFoundException());
    }

}

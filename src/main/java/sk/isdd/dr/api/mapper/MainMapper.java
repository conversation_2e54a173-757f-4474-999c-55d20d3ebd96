package sk.isdd.dr.api.mapper;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.keycloak.representations.idm.UserRepresentation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

import sk.isdd.dr.api.dto.Attachment;
import sk.isdd.dr.api.dto.Consents;
import sk.isdd.dr.api.dto.Log;
import sk.isdd.dr.api.dto.Partner;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.SubjectTestState;
import sk.isdd.dr.api.dto.User;
import sk.isdd.dr.api.dto.csv.ExportedSubject;
import sk.isdd.dr.auth.UserManager;
import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.jpa.entity.AttachmentEntity;
import sk.isdd.dr.jpa.entity.LogEntity;
import sk.isdd.dr.jpa.entity.PartnerEntity;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.entity.TestEntity;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class MainMapper {

    private final Map<String, UserRepresentation> coachesCache = new ConcurrentHashMap<>();

    @Autowired
    private UserManager userManager;

    @Mapping(target = "coach", source = "coachId")
    @Mapping(target = "testState", source = "test")
    @Mapping(target = "attachmentsApprovedAt", source = "shlReportsApprovedAt")
    public abstract Subject mapSubjectEntity(SubjectEntity entity);

    @Mapping(target = "approved", source = "verified")
    @Mapping(target = "approvedAt", source = "verifiedAt")
    @Mapping(target = "hasCV", expression = "java(hasCV(subject))")
    @Mapping(target = "consent1", expression = "java(subject.getConsents() != null && subject.getConsents().isTermsOfProject())")
    @Mapping(target = "consent2", expression = "java(subject.getConsents() != null && subject.getConsents().isProcessingOfPersonalData())")
    @Mapping(target = "consent3", expression = "java(subject.getConsents() != null && subject.getConsents().isMarketing())")
    @Mapping(target = "consent4", expression = "java(subject.getConsents() != null && subject.getConsents().isTestReportsVisibility())")
    public abstract ExportedSubject mapSubjectToExportedSubject(Subject subject);

    public abstract List<ExportedSubject> toExportedSubjects(List<Subject> subjects);

    protected boolean hasCV(Subject subject) {
        return subject.getAttachments() != null &&
               subject.getAttachments().stream()
                       .anyMatch(a -> AttachmentType.CV == a.getType());
    }

    @Mapping(source = "subject.id", target = "subjectId")
    public abstract Attachment mapAttachmentEntity(AttachmentEntity entity);

    @Mapping(source = "email", target = "email")
    @Mapping(source = "firstName", target = "name")
    @Mapping(source = "lastName", target = "surname")
    @Mapping(source = "id", target = "userId")
    @Mapping(target = "id", ignore = true)
    @Mapping(source = "createdTimestamp", target = "createdAt", qualifiedByName = "mapCreatedAt")
    public abstract Subject mapUserToSubject(UserRepresentation userRepresentation);

    @Mapping(target = "subjectId", source = "subject.id")
    @Mapping(target = "user", ignore = true)
    public abstract Log mapAuditLogEntity(LogEntity logEntity);

    @Mapping(target = "role", ignore = true)
    @Mapping(target = "name", source = "firstName")
    @Mapping(target = "surname", source = "lastName")
    @Mapping(target = "email", source = "email")
    public abstract User mapUserRepresentationToUser(UserRepresentation source);

    public abstract Partner mapPartnerEntity(PartnerEntity partnerEntity);

    @Mapping(target = "shlComplete", expression = "java(testEntity.getShlTestedAt() != null)")
    @Mapping(target = "cabinnetComplete", expression = "java(testEntity.getCabinnetResult() != null)")
    @Mapping(target = "verifyOptional", source = "shlTestVerify")
    @Mapping(target = "shlTestLink", source = "shlTestLink")
    public abstract SubjectTestState toDto(TestEntity testEntity);

    @Mapping(target = "termsOfProject", expression = "java((consents & 1) != 0)")
    @Mapping(target = "processingOfPersonalData", expression = "java((consents & 2) != 0)")
    @Mapping(target = "marketing", expression = "java((consents & 4) != 0)")
    @Mapping(target = "testReportsVisibility", expression = "java((consents & 8) != 0)")
    public abstract Consents intConsentsToDTO(Integer consents);

    public Integer consentsDTOToInt(Consents consents) {
        int mask = 0;
        if (consents.isTermsOfProject()) {
            mask |= 1; // bit 1
        }
        if (consents.isProcessingOfPersonalData()) {
            mask |= 2; // bit 2
        }
        if (consents.isMarketing()) {
            mask |= 4; // bit 3
        }
        if (consents.isTestReportsVisibility()) {
            mask |= 8; // bit 4
        }
        return mask;
    }

    protected UserRepresentation mapCoach(String coachId) {
        return coachId == null ? null
                : coachesCache
                        .computeIfAbsent(coachId, r -> {
                            return userManager.getUserById(coachId).orElse(null);
                        });
    }

    @Named("mapCreatedAt")
    protected LocalDateTime mapCreatedAt(Long createdAt) {
        return createdAt != null
            ? LocalDateTime.ofInstant(Instant.ofEpochMilli(createdAt), ZoneId.systemDefault())
            : null;
    }

}

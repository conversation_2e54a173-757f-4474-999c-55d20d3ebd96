package sk.isdd.dr.api.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import sk.isdd.dr.api.service.RecaptchaService;
import sk.isdd.dr.config.RecaptchaProperties;

@Component
public class RecaptchaInterceptor implements HandlerInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecaptchaInterceptor.class);
    private static final String RECAPTCHA_TOKEN_HEADER = "X-Recaptcha-Token";

    private final RecaptchaService recaptchaService;
    private final RecaptchaProperties recaptchaProperties;
    private final ObjectMapper objectMapper;

    public RecaptchaInterceptor(RecaptchaService recaptchaService, RecaptchaProperties recaptchaProperties) {
        this.recaptchaService = recaptchaService;
        this.recaptchaProperties = recaptchaProperties;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!recaptchaProperties.isEnabled()) {
            return true;
        }

        String token = request.getHeader(RECAPTCHA_TOKEN_HEADER);
        
        LOGGER.debug("Processing reCAPTCHA validation for request: {} {}", request.getMethod(), request.getRequestURI());
        
        if (!recaptchaService.validateRecaptcha(token)) {
            LOGGER.warn("reCAPTCHA validation failed for request: {} {}", request.getMethod(), request.getRequestURI());
            
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            
            String errorResponse = objectMapper.writeValueAsString(
                new ErrorResponse("reCAPTCHA validation failed", "RECAPTCHA_VALIDATION_FAILED")
            );
            
            response.getWriter().write(errorResponse);
            return false;
        }

        LOGGER.debug("reCAPTCHA validation successful for request: {} {}", request.getMethod(), request.getRequestURI());
        return true;
    }

    private static class ErrorResponse {
        public final String message;
        public final String code;

        public ErrorResponse(String message, String code) {
            this.message = message;
            this.code = code;
        }
    }
}

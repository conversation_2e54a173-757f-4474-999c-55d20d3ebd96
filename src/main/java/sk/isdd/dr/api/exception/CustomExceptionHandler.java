package sk.isdd.dr.api.exception;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.HandlerMethodValidationException;

import jakarta.transaction.SystemException;
import jakarta.validation.ConstraintViolationException;
import sk.isdd.dr.api.dto.response.ErrorResponse;
import sk.isdd.dr.api.exception.business.BusinessException;
import sk.isdd.dr.api.exception.business.ValidationException;

@ControllerAdvice
@RestControllerAdvice
public class CustomExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomExceptionHandler.class);

    @ExceptionHandler({ BusinessException.class, SystemException.class })
    public ResponseEntity<ErrorResponse> handleRuntime(BusinessException ex) {
        ex.printStackTrace();
        return ResponseEntity
                .status(ex.getStatus())
                .body(new ErrorResponse(ex.getMessage()));
    }

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<Map<String, Object>> handleValidationException(ValidationException ex) {
        return ResponseEntity.badRequest().body(Map.of(
                "error", "Validation failed",
                "fields", ex.getInvalidFields()));
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Map<String, Object>> handleConstraintViolation(ConstraintViolationException ex) {
        List<String> fields = ex.getConstraintViolations()
                .stream()
                .map(cv -> cv.getPropertyPath().toString())
                .toList();

        return ResponseEntity.badRequest().body(Map.of(
                "error", "Validation failed",
                "fields", fields));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, Object>> handleValidationException(MethodArgumentNotValidException ex) {
        List<Map<String, String>> errors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(error -> Map.of(
                        "field", error.getField(),
                        "message", error.getDefaultMessage()))
                .collect(Collectors.toList());

        return ResponseEntity.badRequest().body(Map.of(
                "error", "Validation failed",
                "fields", errors));
    }

    @ExceptionHandler(HandlerMethodValidationException.class)
    public ResponseEntity<Map<String, Object>> handleHandlerMethodValidation(HandlerMethodValidationException ex) {
        List<Map<String, String>> errors = new ArrayList<>();

        // for (ParameterValidationResult paramResult : ex.getAllValidationResults()) {
        // for (ConstraintViolation<?> violation :
        // paramResult.getConstraintViolations()) {
        // errors.add(Map.of(
        // "parameter", paramResult.getExecutableParameter().getName(),
        // "message", violation.getMessage()
        // ));
        // }
        // }

        return ResponseEntity.badRequest().body(Map.of(
                "error", "Validation failed",
                "fields", errors));
    }

}

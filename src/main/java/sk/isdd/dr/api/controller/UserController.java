package sk.isdd.dr.api.controller;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import sk.isdd.dr.api.dto.User;
import sk.isdd.dr.api.dto.request.UpdateUserPasswordRequest;
import sk.isdd.dr.api.dto.request.UpdateUserDataRequest;
import sk.isdd.dr.api.service.api.UserService;
import sk.isdd.dr.common.validation.annotation.PdfFile;

@RestController
@RequestMapping("/user")
@Tag(name = "User Controller")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @PreAuthorize("hasAnyRole('client_pre_registered', 'client_registered', 'client')")
    @PatchMapping(path = "", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(description = "Update profile of logged user")
    public ResponseEntity<User> updateUser(@Valid @RequestPart("update_data") UpdateUserDataRequest request,
            @PdfFile @RequestPart(value = "cv", required = false) MultipartFile cvFile) {
        return ResponseEntity.ok(userService.updateUser(request, cvFile));
    }

    @PreAuthorize("hasAnyRole('client_pre_registered', 'client_registered', 'client')")
    @PatchMapping(path = "/password", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(description = "Update password of logged user")
    public ResponseEntity<User> updateUserPassword(@RequestBody @Valid UpdateUserPasswordRequest request) {
        return ResponseEntity.ok(userService.updateUserPassword(request));
    }

}

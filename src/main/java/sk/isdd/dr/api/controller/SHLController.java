package sk.isdd.dr.api.controller;

import java.net.URI;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import sk.isdd.dr.api.dto.CabinnetTest;
import sk.isdd.dr.api.dto.response.GetCabinnetTestParticipantsResponse;
import sk.isdd.dr.api.dto.response.SuccessResponse;
import sk.isdd.dr.api.dto.shl.ConfirmCandidateReports;
import sk.isdd.dr.api.dto.shl.ConfirmCandidateScores;
import sk.isdd.dr.api.dto.shl.NotifyCandidateReports;
import sk.isdd.dr.api.dto.shl.NotifyCandidateScores;
import sk.isdd.dr.api.service.api.SHLService;
import sk.isdd.dr.api.service.api.TestService;
import sk.isdd.dr.common.validation.annotation.PdfFile;
import sk.isdd.dr.common.validation.annotation.ValidEmail;

@RestController
@RequestMapping("/shl")
@Validated
@Tag(name = "SHL Controller")
public class SHLController {

    @Value("${frontend-url}")
    private String feUrl;

    private final TestService testService;

    private SHLService shlService;

    public SHLController(TestService testService, SHLService shlService) {
        this.testService = testService;
        this.shlService = shlService;
    }

    @PreAuthorize("hasRole('shl')")
    @PostMapping(path = "/import-report", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<SuccessResponse> importReport(@ValidEmail @RequestPart String email,
            @NotNull @PdfFile @RequestPart MultipartFile report) {
        shlService.uploadFinalReport(email, report);
        return ResponseEntity.ok(new SuccessResponse("OK"));
    }

    @PreAuthorize("hasRole('shl')")
    @GetMapping(path = "/cabinnet/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<CabinnetTest> getCabinnetResult(@ValidEmail @RequestParam String email) {
        return ResponseEntity.ok(testService.getCabinnetTestResults(email));
    }

    @PreAuthorize("hasRole('shl')")
    @GetMapping(path = "/cabinnet/participants", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GetCabinnetTestParticipantsResponse> getCabinnetParticipants(
            @RequestParam(name = "time_from") LocalDateTime timeFrom) {
        return ResponseEntity.ok(testService.getCabinnetTestParticipants(timeFrom));
    }

    @PostMapping(path = "/pushback/scores/{subjectId}/{hash}", consumes = MediaType.TEXT_XML_VALUE, produces = MediaType.TEXT_XML_VALUE)
    @SecurityRequirements
    public ResponseEntity<ConfirmCandidateScores> processSubjectScores(@PathVariable Integer subjectId, @PathVariable String hash,
            @RequestBody NotifyCandidateScores request) {
        return ResponseEntity.ok(shlService.processScoresPushback(subjectId, hash, request));
    }

    @PostMapping(path = "/pushback/reports/{subjectId}/{hash}", consumes = MediaType.TEXT_XML_VALUE, produces = MediaType.TEXT_XML_VALUE)
    @SecurityRequirements
    public ResponseEntity<ConfirmCandidateReports> processSubjectReports(@PathVariable Integer subjectId, @PathVariable String hash,
            @RequestBody NotifyCandidateReports request) {
        return ResponseEntity.ok(shlService.processReportsPushback(subjectId, hash, request));
    }

    @GetMapping(path = "/callback/complete/{subjectId}/{hash}")
    @SecurityRequirements
    public ResponseEntity<Void> processCompleteCallback(@PathVariable Integer subjectId, @PathVariable String hash) {
        shlService.processTestCompleteCallback(subjectId, hash);
        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(URI.create(feUrl + "/testovanie"));
        return new ResponseEntity<>(headers, HttpStatus.FOUND);
    }

    @GetMapping(path = "/callback/not-complete/{subjectId}/{hash}")
    @SecurityRequirements
    public ResponseEntity<Void> processCancelCallback(@PathVariable Integer subjectId, @PathVariable String hash) {
        shlService.processTestNotCompleteCallback(subjectId, hash);
        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(URI.create(feUrl + "/testovanie"));
        return new ResponseEntity<>(headers, HttpStatus.FOUND);
    }

}

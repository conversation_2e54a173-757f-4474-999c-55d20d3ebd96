package sk.isdd.dr.api.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import sk.isdd.dr.api.dto.request.StoreCabinnetResultRequest;
import sk.isdd.dr.api.dto.response.StoreCabinnetResultResponse;
import sk.isdd.dr.api.service.api.TestService;
import sk.isdd.dr.auth.CurrentUser;

@RestController
@RequestMapping("/test")
@Tag(name = "Test Controller")
public class TestController {

    private final TestService testService;

    public TestController(TestService testService) {
        this.testService = testService;
    }

    @PreAuthorize("hasRole('client')")
    @PostMapping(path = "/cabinnet")
    @Operation(description = "Store results of CABINNET test")
    public ResponseEntity<StoreCabinnetResultResponse> storeCabinnetResults(@AuthenticationPrincipal CurrentUser currentUser,
            @Valid @RequestBody StoreCabinnetResultRequest request) {
        return ResponseEntity.ok(testService.storeCabinnetTestResult(currentUser.getSubjectId(), request));
    }

}

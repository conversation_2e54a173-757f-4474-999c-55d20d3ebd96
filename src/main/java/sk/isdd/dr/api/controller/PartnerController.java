package sk.isdd.dr.api.controller;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import sk.isdd.dr.api.dto.Partner;
import sk.isdd.dr.api.dto.request.InvitePartnerClientsRequest;
import sk.isdd.dr.api.dto.response.InvitePartnerClientsResponse;
import sk.isdd.dr.api.exception.business.NotFoundException;
import sk.isdd.dr.api.service.api.PartnerService;
import sk.isdd.dr.auth.CurrentUser;

@RestController
@RequestMapping("/partner")
public class PartnerController {

    private final PartnerService partnerService;

    public PartnerController(PartnerService partnerService) {
        this.partnerService = partnerService;
    }

    @PreAuthorize("hasAnyRole('partner_admin', 'client_registered', 'client_pre_registered', 'client')")
    @GetMapping(path = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Partner> getPartnerDetails(@AuthenticationPrincipal CurrentUser currentUser) {
        if (currentUser.getPartnerId() == null) {
            throw new NotFoundException("Partner not found");
        }
        return ResponseEntity.ok(partnerService.getPartner(currentUser.getPartnerId()));
    }

    @PreAuthorize("hasRole('partner_admin')")
    @PatchMapping(path = "", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Partner> updatePartner(@AuthenticationPrincipal CurrentUser currentUser, @RequestBody Partner partner) {
        if (currentUser.getPartnerId() == null) {
            throw new NotFoundException("Partner not found");
        }
        return ResponseEntity.ok(partnerService.updatePartner(currentUser.getPartnerId(), partner));
    }

    @PreAuthorize("hasRole('partner_admin')")
    @PostMapping(path = "/subject/invite", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<InvitePartnerClientsResponse> inviteClients(@AuthenticationPrincipal CurrentUser currentUser, @RequestBody InvitePartnerClientsRequest request) {
        if (currentUser.getPartnerId() == null) {
            throw new NotFoundException("Partner not found");
        }
        return ResponseEntity.ok(partnerService.inviteClients(currentUser.getPartnerId(), request));
    }

}

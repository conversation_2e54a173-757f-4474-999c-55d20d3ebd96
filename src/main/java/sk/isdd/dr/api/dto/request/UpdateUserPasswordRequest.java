package sk.isdd.dr.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class UpdateUserPasswordRequest {

    @NotEmpty
    @JsonProperty(value = "password", required = true)
    private String password;

    @NotEmpty
    @JsonProperty(value = "password_verify", required = true)
    private String passwordVerify;

}

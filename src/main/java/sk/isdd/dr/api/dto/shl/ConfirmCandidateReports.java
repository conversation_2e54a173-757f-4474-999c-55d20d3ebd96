package sk.isdd.dr.api.dto.shl;

import javax.xml.datatype.XMLGregorianCalendar;

import com.cebtalentcentral.Confirm;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import lombok.Getter;
import lombok.Setter;

@XmlRootElement(name = "ConfirmCandidateReports", namespace = "http://cebtalentcentral.com")
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public class ConfirmCandidateReports {

    @XmlAttribute(name = "majorVersionId")
    private String majorVersionId = "1";

    @XmlAttribute(name = "minorVersionId")
    private String minorVersionId = "0";

    @XmlElement(name = "ApplicationArea", required = true, namespace = "http://cebtalentcentral.com")
    private ApplicationArea applicationArea;

    @XmlElement(name = "Confirm", required = true, namespace = "http://cebtalentcentral.com")
    private Confirm confirm;

    @XmlAccessorType(XmlAccessType.FIELD)
    @Getter
    @Setter
    public static class ApplicationArea {

        @XmlElement(name = "CreationDateTime", required = true, namespace = "http://cebtalentcentral.com")
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar creationDateTime;

    }

}

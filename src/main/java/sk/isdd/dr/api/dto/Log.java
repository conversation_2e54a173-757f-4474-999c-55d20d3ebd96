package sk.isdd.dr.api.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import sk.isdd.dr.common.enums.LogAction;

@Data
public class Log {

    @JsonProperty(value = "id", required = true)
    private Integer id;

    @JsonProperty(value = "created_at", required = true)
    private LocalDateTime createdAt;

    @JsonProperty(value = "user")
    private User user;

    @JsonProperty(value = "subject_id", required = true)
    private String subjectId;

    @JsonProperty(value = "action", required = true)
    private LogAction action;

    @JsonProperty(value = "message")
    private String message;

    @JsonProperty(value = "extra_data")
    private String extraData;

}

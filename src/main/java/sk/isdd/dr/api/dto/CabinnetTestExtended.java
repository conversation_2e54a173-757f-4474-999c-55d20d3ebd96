package sk.isdd.dr.api.dto;

import java.util.List;

import org.springframework.validation.annotation.Validated;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import sk.isdd.dr.common.enums.CareerChange;

@Getter
@Setter
@Validated
public class CabinnetTestExtended extends CabinnetTest {

    @NotEmpty
    @JsonProperty(value = "actual_work", required = true)
    private String actualWork;

    @NotNull
    @Size(min = 1)
    @JsonProperty(value = "career_change", required = true)
    private List<CareerChange> careerChange;

    @JsonProperty(value = "other_option")
    private String otherOption;

    @NotEmpty
    @JsonProperty(value = "expected_work", required = true)
    private String expectedWork;

}

package sk.isdd.dr.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import sk.isdd.dr.auth.UserRole;

@Data
public class User {

    @JsonProperty(value = "id", required = true)
    private String id;

    @JsonProperty(value = "role", required = true)
    private UserRole role;
    
    @JsonProperty(value = "name", required = true)
    private String name;

    @JsonProperty(value = "surname", required = true)
    private String surname;

    @JsonProperty(value = "email", required = true)
    private String email;

}

package sk.isdd.dr.api.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import sk.isdd.dr.common.enums.AttachmentType;

@Data
public class Attachment {

    @JsonProperty(value = "id", required = true)
    private Integer id;

    @JsonProperty(value = "created_at", required = true)
    private LocalDateTime createdAt;

    @JsonProperty(value = "modified_at")
    private LocalDateTime modifiedAt;

    @JsonProperty(value = "subject_id", required = true)
    private Integer subjectId;

    @JsonProperty(value = "type", required = true)
    private AttachmentType type;

    @JsonProperty(value = "mime_type", required = true)
    private String mimeType;

    @JsonProperty(value = "file_name", required = true)
    private String fileName;
    
}

package sk.isdd.dr.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import sk.isdd.dr.api.dto.Consents;
import sk.isdd.dr.common.validation.annotation.ValidLinkedIn;

@Data
public class UpdateUserDataRequest {

    @NotBlank
    @JsonProperty(value = "name", required = true)
    private String name;

    @NotBlank
    @JsonProperty(value = "surname", required = true)
    private String surname;

    // TODO change after testing
    // @BirthNumber
    @NotBlank
    @JsonProperty(value = "id_number", required = true)
    private String idNumber;

    @JsonProperty(value = "phone_number")
    private String phoneNumber;

    @ValidLinkedIn
    @JsonProperty(value = "linked_in_profile")
    private String linkedInProfile;

    @NotNull
    @JsonProperty(value = "consents", required = true)
    private Consents consents;

}

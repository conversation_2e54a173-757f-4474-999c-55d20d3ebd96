package sk.isdd.dr.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class SubjectTestState {

    @JsonProperty(value = "cabinnet_complete", required = true)
    private boolean cabinnetComplete;

    @JsonProperty(value = "shl_complete", required = true)
    private boolean shlComplete;

    @JsonProperty(value = "verify_optional", required = true)
    private boolean verifyOptional;

    @JsonProperty(value = "shl_test_link")
    private String shlTestLink;

}

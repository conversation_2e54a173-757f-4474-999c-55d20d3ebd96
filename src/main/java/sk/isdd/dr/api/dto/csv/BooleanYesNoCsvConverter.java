package sk.isdd.dr.api.dto.csv;

import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;

public class BooleanYesNoCsvConverter extends AbstractBeanField<Boolean, String> {

    @Override
    protected String convertToWrite(Object value) {
        if (value == null) return "";
        return (Boolean) value ? "áno" : "nie";
    }

    @Override
    protected Object convert(String value) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'convert'");
    }
}

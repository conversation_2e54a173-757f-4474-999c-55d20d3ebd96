package sk.isdd.dr.api.dto;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.validation.annotation.Validated;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import sk.isdd.dr.common.enums.SubjectEducationLevel;

@Getter
@Setter
@Validated
public class CabinnetTest {

    @JsonProperty(value = "created_at", required = true)
    private LocalDateTime createdAt;

    @NotNull
    @JsonProperty(value = "education_level", required = true)
    private SubjectEducationLevel educationLevel;

    @NotEmpty
    @JsonProperty(value = "results", required = true)
    private List<CabinnetTestQuestionAnswer> results;

}

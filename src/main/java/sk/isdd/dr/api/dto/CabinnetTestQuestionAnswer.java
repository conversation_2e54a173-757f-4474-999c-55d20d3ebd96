package sk.isdd.dr.api.dto;

import org.springframework.validation.annotation.Validated;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Data
@Validated
public class CabinnetTestQuestionAnswer {

    @Positive
    @JsonProperty(value = "question_id", required = true)
    private Integer questionId;

    @Min(1)
    @Max(5)
    @JsonProperty(value = "answer", required = true)
    private Integer answer;

}

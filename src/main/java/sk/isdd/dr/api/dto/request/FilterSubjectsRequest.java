package sk.isdd.dr.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import sk.isdd.dr.api.dto.Pagination;
import sk.isdd.dr.common.enums.SubjectState;

@Data
public class FilterSubjectsRequest {

    @JsonProperty(value = "pagination", required = true)
    private Pagination pagination;

    @JsonProperty("coach_id")
    private String coachId;

    @JsonProperty("partner_id")
    private Integer partnerId;

    @JsonProperty("id_number")
    private String idNumber;

    @JsonProperty("query")
    private String query;

    @JsonProperty("state")
    private SubjectState state;

}

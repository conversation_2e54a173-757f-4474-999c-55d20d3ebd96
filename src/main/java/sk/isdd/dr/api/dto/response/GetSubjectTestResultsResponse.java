package sk.isdd.dr.api.dto.response;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import sk.isdd.dr.api.dto.CabinnetTestQuestionAnswer;
import sk.isdd.dr.common.enums.SubjectEducationLevel;

@Data
@Deprecated
public class GetSubjectTestResultsResponse {

    @JsonProperty(value = "education_level", required = true)
    private SubjectEducationLevel educationLevel;

    @JsonProperty(value = "results", required = true)
    private List<CabinnetTestQuestionAnswer> results;

}

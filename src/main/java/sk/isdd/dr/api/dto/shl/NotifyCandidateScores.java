package sk.isdd.dr.api.dto.shl;

import com.cebtalentcentral.ApplicationArea;
import com.cebtalentcentral.CandidateScoresDataArea.CandidateScores;
import com.cebtalentcentral.CandidateScoresDataArea.CustomerParty;
import com.cebtalentcentral.Notify;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Getter;
import lombok.Setter;

@XmlRootElement(name = "NotifyCandidateScores", namespace = "http://cebtalentcentral.com")
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public class NotifyCandidateScores {

    @XmlElement(name = "ApplicationArea", required = true, namespace = "http://cebtalentcentral.com")
    private ApplicationArea applicationArea;

    @XmlElement(name = "Notify", required = true, namespace = "http://cebtalentcentral.com")
    private Notify notify;

    @XmlElement(name = "DataArea", required = true, namespace = "http://cebtalentcentral.com")
    private DataArea dataArea;

    @XmlAccessorType(XmlAccessType.FIELD)
    @Getter
    @Setter
    public static class DataArea {

        @XmlElement(name = "CandidateScores", required = true, namespace = "http://cebtalentcentral.com")
        private CandidateScores scores;

        @XmlElement(name = "CustomerParty", required = true, namespace = "http://cebtalentcentral.com")
        private CustomerParty customerParty;

    }

}

package sk.isdd.dr.api.dto.chatbot;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ClaudeResponse {

    private String id;
    private String type;
    private String role;
    private List<ClaudeContent> content;
    private String model;
    private String stop_reason;
    private String stop_sequence;

    @Getter
    @Setter
    public static class ClaudeContent {
        private String type;
        private String text;
    }

}

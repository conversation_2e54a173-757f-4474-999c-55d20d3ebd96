package sk.isdd.dr.api.dto.request;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import sk.isdd.dr.common.validation.annotation.ValidEmail;

@Data
public class InvitePartnerClientsRequest {

    @JsonProperty(value = "invite_text")
    private String inviteText;

    @NotEmpty
    @JsonProperty(value = "emails", required = true)
    private List<@ValidEmail String> emails;

}

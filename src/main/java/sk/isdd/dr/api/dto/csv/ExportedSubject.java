package sk.isdd.dr.api.dto.csv;

import java.time.LocalDateTime;

import com.opencsv.bean.CsvBindByPosition;
import com.opencsv.bean.CsvCustomBindByPosition;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ExportedSubject {

    @CsvCustomBindByPosition(position = 0, converter = LocalDateTimeCsvConverter.class)
    private LocalDateTime createdAt;

    @CsvCustomBindByPosition(position = 1, converter = BooleanYesNoCsvConverter.class)
    private boolean approved;

    @CsvBindByPosition(position = 2)
    private String name;

    @CsvBindByPosition(position = 3)
    private String surname;

    @CsvBindByPosition(position = 4)
    private String idNumber;

    @CsvBindByPosition(position = 5)
    private String phoneNumber;

    @CsvBindByPosition(position = 6)
    private String email;

    @CsvCustomBindByPosition(position = 7, converter = BooleanYesNoCsvConverter.class)
    private boolean hasCV;

    @CsvBindByPosition(position = 8)
    private String linkedInProfile;

    @CsvCustomBindByPosition(position = 9, converter = BooleanYesNoCsvConverter.class)
    private boolean consent1;

    @CsvCustomBindByPosition(position = 10, converter = BooleanYesNoCsvConverter.class)
    private boolean consent2;

    @CsvCustomBindByPosition(position = 11, converter = BooleanYesNoCsvConverter.class)
    private boolean consent3;

    @CsvCustomBindByPosition(position = 12, converter = BooleanYesNoCsvConverter.class)
    private boolean consent4;

    @CsvCustomBindByPosition(position = 13, converter = LocalDateTimeCsvConverter.class)
    private LocalDateTime approvedAt;

    @CsvCustomBindByPosition(position = 14, converter = LocalDateTimeCsvConverter.class)
    private LocalDateTime coachRequestedAt;

}

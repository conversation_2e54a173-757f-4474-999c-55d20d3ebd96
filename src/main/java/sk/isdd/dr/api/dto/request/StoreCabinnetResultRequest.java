package sk.isdd.dr.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import sk.isdd.dr.api.dto.CabinnetTestExtended;

@Data
public class StoreCabinnetResultRequest {

    @JsonProperty(value = "verify_test")
    private boolean verifyTest;

    @NotNull
    @JsonProperty(value = "result", required = true)
    private CabinnetTestExtended result;

}

package sk.isdd.dr.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import sk.isdd.dr.api.dto.Consents;
import sk.isdd.dr.common.validation.annotation.NotDisposableEmail;

@Data
public class RegisterRequest {

    @NotBlank
    @NotDisposableEmail
    @JsonProperty(value = "email", required = true)
    private String email;

    @NotNull
    @JsonProperty(value = "consents", required = true)
    private Consents consents;

}

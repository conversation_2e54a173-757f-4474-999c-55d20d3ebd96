package sk.isdd.dr.api.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class Subject {

    @JsonProperty(value = "id", required = true)
    private Integer id;

    @JsonProperty(value = "created_at", required = true)
    private LocalDateTime createdAt;

    @JsonProperty(value = "modified_at")
    private LocalDateTime modifiedAt;

    @JsonProperty(value = "verified_at")
    private LocalDateTime verifiedAt;

    @JsonProperty(value = "deleted_at")
    private LocalDateTime deletedAt;

    @JsonProperty(value = "coach_requested_at")
    private LocalDateTime coachRequestedAt;

    @JsonProperty(value = "attachments_approved_at")
    private LocalDateTime attachmentsApprovedAt;

    @JsonProperty(value = "verified")
    private boolean verified;

    @JsonProperty(value = "name")
    private String name;

    @JsonProperty(value = "surname")
    private String surname;

    @JsonProperty(value = "id_number")
    private String idNumber;

    @JsonProperty(value = "email", required = true)
    private String email;

    @JsonProperty(value = "phone_number")
    private String phoneNumber;

    @JsonProperty(value = "linked_in_profile")
    private String linkedInProfile;

    @JsonProperty("partner")
    private Partner partner;

    @JsonProperty(value = "coach")
    private User coach;

    @JsonProperty(value = "user_id", required = true)
    private String userId;

    @JsonProperty(value = "test_state")
    private SubjectTestState testState;

    @JsonProperty(value = "consents", required = true)
    private Consents consents;

    private List<Attachment> attachments;

}

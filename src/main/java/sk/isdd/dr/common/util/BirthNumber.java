package sk.isdd.dr.common.util;

public class BirthNumber {

    private final String rawNumber;
    private final String cleanedNumber;

    public BirthNumber(String number) {
        this.rawNumber = number != null ? number : "";
        this.cleanedNumber = this.rawNumber.replaceAll("[/\\s]", "");
    }

    public boolean isValid() {
        if (cleanedNumber.length() < 9 || cleanedNumber.length() > 10) {
            return false;
        }

        // kontrola deliteľnosti 11 pri 10-miestnom čísle
        if (cleanedNumber.length() == 10) {
            try {
                long numberAsLong = Long.parseLong(cleanedNumber);
                if (numberAsLong % 11 != 0) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }

        int month = getMonth();
        int day = getDay();

        return month >= 1 && month <= 12 && day >= 1 && day <= 31;
    }

    public int getYear() {
        int year = Integer.parseInt(cleanedNumber.substring(0, 2));
        return (cleanedNumber.length() == 9 || year >= 54) ? 1900 + year : 2000 + year;
    }

    public int getMonth() {
        int month = Integer.parseInt(cleanedNumber.substring(2, 4));
        return (month > 50) ? month - 50 : month;
    }

    public int getDay() {
        return Integer.parseInt(cleanedNumber.substring(4, 6));
    }

    public String getRawNumber() {
        return rawNumber;
    }

    public String getCleanedNumber() {
        return cleanedNumber;
    }
}

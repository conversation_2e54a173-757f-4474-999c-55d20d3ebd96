package sk.isdd.dr.common.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.UUID;

public class AuthUtils {

    private static final String PASS_CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_+=<>?";

    public static String generateSha256Token() {
        return convertToSHA256Hex(UUID.randomUUID().toString() + System.nanoTime());
    }

    public static String convertToSHA256Hex(String message) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(message.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }

    public static boolean passwordIsCorrect(String givenPassword, String userPassword) {
        return convertToSHA256Hex(givenPassword).equals(convertToSHA256Hex(userPassword));
    }

    public static boolean passwordIsValid(String password) {
        return password.length() >= 8;
    }

    public static String randomPassword(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Password length must be positive");
        }

        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(PASS_CHARACTERS.length());
            password.append(PASS_CHARACTERS.charAt(index));
        }

        return password.toString();

    }

    private static String bytesToHex(byte[] bytes) {
        return java.util.HexFormat.of().formatHex(bytes);
    }

}

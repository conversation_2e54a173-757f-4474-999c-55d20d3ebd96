package sk.isdd.dr.common.enums;

public enum AttachmentType {

    CV, // zivotopis pri registracii
    SIMPLE, // vystupna sprava z SHL
    SIMPLE_ENCRYPTED,
    FULL1, // vysledok SHL testu 1 (NEDOSTUPNE PRE KLIENTA) R -- kouc a admin
    FULL2, // vysledok SHL testu 2 (NEDOSTUPNE PRE KLIENTA) R -- kouc a admin
    COACH_REPORT, // sprava z koucingu
    COACH_REPORT_ENCRYPTED

}

// GET /api/subject/attachment/list                             (PRIHLASENY KLIENT)
// GET /api/subject/attachment/{attachmentId}                   (PRIHLASENY KLIENT)

// GET /api/subject/{subjectId}/attachment/list                 (ADMIN)
// GET /api/subject/{subjectId}/attachment/{attachmentId}       (ADMIN)

// COACH_REPORT    PUT /api/subject/{subjectId}/coach/report    (ADMIN A KOUC)
// CV              PUT /api/subject/{subjectId}/cv              (KLIENT PRI REGISTRACII)
// SIMPLE          PUT /api/shl/report                          (SHL)

// POST /api/subject/{subjectId}/shl/report/approve (ADMIN)





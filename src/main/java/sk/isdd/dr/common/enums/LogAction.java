package sk.isdd.dr.common.enums;

public enum LogAction {
    REGISTER,
    REG<PERSON>TER_EMAIL_SENT,
    ZOZ_VERIFICATION,
    ATTACHMENT_ADDED,
    ATTACHMENT_DELETED,
    COACH_REQUESTED,
    COACH_ASSIGNED,
    <PERSON>ER_PASSWORD_CHANGE,
    <PERSON>ER_PASSWORD_RESET,
    USER_UPDATED,
    USER_DELETED,
    SUBJECT_UPDATED,
    SUBJECT_DELETED,
    SUBJECT_REPORTS_APPROVED,
    LOGIN,
    ADMIN_CREATE_USER,
    R<PERSON>IS<PERSON><PERSON><PERSON>_DELETE,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_APPROVE,
    R<PERSON><PERSON><PERSON><PERSON><PERSON>_DECLINE,
    <PERSON>OUNT_GENERATED,
    <PERSON>MAIL_SENT,
    SHL_SCORE_IMPORTED,
    SHL_JOB_MATCHING_IMPORTED,
    ACCOUNT_EMAIL_SENT,
    ADMIN_LOGIN,
    EMAIL_ZOZ_NOT_VERIFIED,
    SHL_JOB_MEASUREMENT_IMPORTED,

    // new

    SHL_TEST_COMPLETE,
    SHL_TEST_NOT_COMPLETE,
    SHL_CABINNET_REQUESTED,
    CABINNET_COMPLETE,
    REGISTER_COMPLETE
    
}

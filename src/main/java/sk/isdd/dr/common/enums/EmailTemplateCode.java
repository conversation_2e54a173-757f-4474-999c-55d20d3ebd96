package sk.isdd.dr.common.enums;

public enum EmailTemplateCode {

    // legacy

    REGISTER_PASSWORD,
    REGISTER_ZOZ_VERIFIED,
    REGISTER_NON_ZOZ,
    REGISTE<PERSON>_ZOZ_FAILED,
    <PERSON><PERSON><PERSON>TE<PERSON>_INTEREST,
    ACCOUNT_CREATED,
    REG<PERSON><PERSON><PERSON>ION_DECLINED,
    TEST_READY,
    TEST_RESULTS_CREATED_ACCOUNT,
    TEST_RESULTS,
    COACH_REQUEST,
    REGISTER_ZOZ_REMINDER,
    TMP_PASSWORD,

    // new

    PRE_REGISTER_PASSWORD,
    PARTNER_INVITATION,
    NOTIFICATION_BEGIN_TESTING,
    NOTIFICATION_FINISH_TESTING,
    NOTIFICATION_COUCHING,
    COACH_REPORT,
    REGISTRATION_APPROVED,
    COACH_REQUEST_ADMIN

}


package sk.isdd.dr.common.validation.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import sk.isdd.dr.common.validation.annotation.PdfFile;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

public class PdfFileValidator implements ConstraintValidator<PdfFile, MultipartFile> {

    @Override
    public boolean isValid(MultipartFile file, ConstraintValidatorContext context) {
        if (file == null || file.isEmpty()) {
            return true;
        }

        String contentType = file.getContentType();
        String filename = file.getOriginalFilename();

        return Objects.equals(contentType, "application/pdf") &&
                filename != null &&
                filename.toLowerCase().endsWith(".pdf") &&
                hasPdfMagicBytes(file) &&
                isParsablePdf(file);
    }

    private boolean hasPdfMagicBytes(MultipartFile file) {
        try (InputStream is = file.getInputStream()) {
            byte[] header = new byte[4];
            int read = is.read(header, 0, 4);
            return read == 4 &&
                    header[0] == 0x25 &&
                    header[1] == 0x50 &&
                    header[2] == 0x44 &&
                    header[3] == 0x46;
        } catch (IOException e) {
            return false;
        }
    }

    private boolean isParsablePdf(MultipartFile file) {
        try (InputStream is = file.getInputStream();
                PDDocument doc = PDDocument.load(is)) {
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}

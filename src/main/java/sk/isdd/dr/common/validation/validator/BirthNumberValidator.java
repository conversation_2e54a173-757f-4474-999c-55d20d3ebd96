package sk.isdd.dr.common.validation.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import sk.isdd.dr.common.util.BirthNumber;

public class BirthNumberValidator implements ConstraintValidator<sk.isdd.dr.common.validation.annotation.BirthNumber, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }

        BirthNumber birthNumber = new BirthNumber(value);
        return birthNumber.isValid();
    }
}

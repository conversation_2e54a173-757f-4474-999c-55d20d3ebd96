package sk.isdd.dr.common.validation.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import sk.isdd.dr.common.validation.validator.LinkedInUrlValidator;

@Documented
@Constraint(validatedBy = LinkedInUrlValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidLinkedIn {

    String message() default "Invalid LinkedIn URL";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}

package sk.isdd.dr.common.validation.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import sk.isdd.dr.common.validation.validator.ValidEmailValidator;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = ValidEmailValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidEmail {

    String message() default "Invalid email";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

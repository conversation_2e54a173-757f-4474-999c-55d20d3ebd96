package sk.isdd.dr.common.validation.validator;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import sk.isdd.dr.common.validation.annotation.NotDisposableEmail;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class DisposableEmailValidator implements ConstraintValidator<NotDisposableEmail, String> {

    public static final String DISPOSABLE_EMAIL_DOMAINS_FILE_PATH = "disposable-email-domains-blacklist.conf";
    // public static final String ADDITIONAL_DOMAINS_CONFIG_PARAM =
    // "disposableEmailDomains.additional";

    // private final Environment env;
    private Set<String> disposableEmailDomains;

    // public DisposableEmailValidator(Environment env) {
    // this.env = env;
    // }

    @Override
    public void initialize(NotDisposableEmail constraintAnnotation) {
        // Set<String> additionalDomains =
        // env.getProperty(ADDITIONAL_DOMAINS_CONFIG_PARAM, Set.class,
        // Collections.emptySet());

        try {
            ClassPathResource resource = new ClassPathResource(DISPOSABLE_EMAIL_DOMAINS_FILE_PATH);
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
                disposableEmailDomains = reader.lines()
                        .map(String::trim)
                        .filter(line -> !line.isEmpty() && !line.startsWith("#"))
                        .map(String::toLowerCase)
                        .collect(Collectors.toSet());
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize disposable email domains list", e);
        }
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (!StringUtils.hasText(value)) {
            return true;
        }

        String domain = extractDomain(value);
        if (domain == null) {
            return false;
        }

        return !disposableEmailDomains.contains(domain.toLowerCase());
    }

    private String extractDomain(String email) {
        int atIndex = email.lastIndexOf('@');
        if (atIndex == -1 || atIndex == email.length() - 1) {
            return null;
        }
        return email.substring(atIndex + 1);
    }

}

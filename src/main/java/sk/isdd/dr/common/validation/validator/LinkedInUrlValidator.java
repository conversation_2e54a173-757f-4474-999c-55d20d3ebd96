package sk.isdd.dr.common.validation.validator;

import org.springframework.util.StringUtils;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import sk.isdd.dr.common.validation.annotation.ValidLinkedIn;

public class LinkedInUrlValidator implements ConstraintValidator<ValidLinkedIn, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || !StringUtils.hasText(value)) {
            return true;
        }

        return value.contains("linkedin.com/in/");
    }

}

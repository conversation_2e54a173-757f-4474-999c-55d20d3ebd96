package sk.isdd.dr.common.validation.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import sk.isdd.dr.common.validation.validator.BirthNumberValidator;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = BirthNumberValidator.class)
@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface BirthNumber {

    String message() default "Invalid birth number";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

package sk.isdd.dr.jpa.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import sk.isdd.dr.jpa.entity.TestEntity;

public interface TestRepository extends JpaRepository<TestEntity, Integer> {

    List<TestEntity> findByCreatedAtGreaterThan(LocalDateTime timeFrom);

    Optional<TestEntity> findBySubjectEmail(String email);

    @Query("SELECT t FROM TestEntity t JOIN t.subject s WHERE t.shlTestLink IS NOT NULL AND t.shlTestedAt IS NULL AND t.createdAt <= :thresholdDate AND s.deletedAt IS NULL")
    List<TestEntity> getUnfinishedTests(@Param("thresholdDate") LocalDateTime thresholdDate);

}

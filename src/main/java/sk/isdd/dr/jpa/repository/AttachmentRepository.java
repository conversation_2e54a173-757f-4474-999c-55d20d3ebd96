package sk.isdd.dr.jpa.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.jpa.entity.AttachmentEntity;
import sk.isdd.dr.jpa.entity.SubjectEntity;

public interface AttachmentRepository extends JpaRepository<AttachmentEntity, Integer> {

    Optional<AttachmentEntity> findBySubjectIdAndId(Integer subjectId, Integer id);

    List<AttachmentEntity> findBySubject(SubjectEntity subject);

    Optional<AttachmentEntity> findBySubjectAndType(SubjectEntity subject, AttachmentType type);

    Optional<AttachmentEntity> findBySubjectIdAndType(Integer subjectId, AttachmentType type);

}

package sk.isdd.dr.jpa.entity.base;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

@MappedSuperclass
@Getter
@Setter
public abstract class ModifiableEntity extends BaseEntity {

    @Column(name = "modified", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP", insertable = true, updatable = true)
    protected LocalDateTime modifiedAt;

    @Column(name = "flags")
    private Integer flags;

}

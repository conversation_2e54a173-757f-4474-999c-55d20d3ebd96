package sk.isdd.dr.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import sk.isdd.dr.common.enums.CodebookType;
import sk.isdd.dr.jpa.entity.base.ModifiableEntity;

@Entity
@Table(name = "codebook")
@Getter
@Setter
public class CodeBookEntity extends ModifiableEntity {

    @Column(name = "parent_id")
    private Integer parentId;

    @Column(name = "type", columnDefinition = "VARCHAR(32)", nullable = false, updatable = false)
    @Enumerated(EnumType.STRING)
    private CodebookType type;

    @Column(name = "code", length = 16, nullable = false, updatable = false, unique = true)
    private String code;

    @Column(name = "title", length = 64, nullable = false, updatable = false, unique = true)
    private String title;

    @Column(name = "subtitle", length = 64, nullable = false, updatable = false)
    private String subTitle;

}

package sk.isdd.dr.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.jpa.entity.base.BaseEntity;

@Entity
@Table(name = "log")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LogEntity extends BaseEntity {

    @Column(name = "user_id", updatable = false)
    private String userId;

    @ManyToOne
    @JoinColumn(name = "subject_id", nullable = false, updatable = false)
    private SubjectEntity subject;

    @Column(name = "action", columnDefinition = "VARCHAR(32)", nullable = false, updatable = false)
    @Enumerated(EnumType.STRING)
    private LogAction action;

    @Column(name = "message", updatable = false)
    private String message;

}

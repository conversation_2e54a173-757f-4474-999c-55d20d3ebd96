package sk.isdd.dr.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.jpa.entity.base.ModifiableEntity;

@Entity
@Table(name = "attachment")
@Getter
@Setter
public class AttachmentEntity extends ModifiableEntity {

    @ManyToOne
    @JoinColumn(name = "subject_id", nullable = false)
    private SubjectEntity subject;

    @Column(name = "type", columnDefinition = "VARCHAR(32)", nullable = false)
    @Enumerated(EnumType.STRING)
    private AttachmentType type;

    @Column(name = "mimetype", nullable = false, length = 32)
    private String mimeType;

    @Column(name = "filename", nullable = false, length = 128)
    private String fileName;

}

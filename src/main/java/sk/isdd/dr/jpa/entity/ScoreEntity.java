package sk.isdd.dr.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import sk.isdd.dr.jpa.entity.base.ModifiableEntity;

@Entity
@Table(name = "score")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ScoreEntity extends ModifiableEntity {

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "score")
    private Double score;

    @Column(name = "type", nullable = false)
    private String type;

    @ManyToOne()
    @JoinColumn(name = "subject_id", nullable = false)
    private SubjectEntity subject;

}

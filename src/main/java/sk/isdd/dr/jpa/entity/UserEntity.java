package sk.isdd.dr.jpa.entity;

import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import sk.isdd.dr.jpa.entity.base.ModifiableEntity;

@Entity
@Table(name = "user")
@Getter
@Setter
public class UserEntity  extends ModifiableEntity {

    @Column(name = "deleted")
    private LocalDateTime deletedAt;

    @JoinColumn(name = "subject_id", nullable = true)
    @OneToOne()
    private SubjectEntity subject;

    @Column(name = "type")
    private String type;

    @Column(name = "email", unique = true, nullable = false, updatable = false)
    private String email;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "password", nullable = false)
    private String password;

    @Column(name = "password_salt", nullable = false)
    private String passwordSalt;

    @Column(name = "access_token")
    private String accessToken;

    @Column(name = "refresh_token")
    private String refreshToken;

    @Column(name = "expires_at")
    private LocalDateTime tokenExpiresAt;

}

package sk.isdd.dr.jpa.entity;

import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import sk.isdd.dr.jpa.entity.base.ModifiableEntity;

@Entity
@Table(name = "partner")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PartnerEntity extends ModifiableEntity {

    @Column(name = "claim", length = 160, nullable = false)
    private String claim;

    @Column(name = "logo", nullable = false)
    private String logo;

    @OneToMany(mappedBy = "partner")
    private List<SubjectEntity> subjects;

}

package sk.isdd.dr.jpa.entity;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import sk.isdd.dr.jpa.entity.base.ModifiableEntity;

@Entity
@Table(name = "subject")
@Getter
@Setter
public class SubjectEntity extends ModifiableEntity {

    @Column(name = "verified_at")
    private LocalDateTime verifiedAt;

    @Column(name = "verified")
    private boolean verified;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    @Column(name = "coach_requested_at")
    private LocalDateTime coachRequestedAt;

    @Column(name = "shl_reports_approved_at")
    private LocalDateTime shlReportsApprovedAt;

    @Column(name = "consents")
    private Integer consents;

    @Column(name = "user_id", length = 32)
    private String userId;

    @Column(name = "coach_id")
    private String coachId;

    @Column(name = "type", nullable = false)
    private String type;

    @Column(name = "name", length = 64)
    private String name;

    @Column(name = "surname", length = 64)
    private String surname;

    @Column(name = "id_number", length = 10)
    private String idNumber;

    @Column(name = "email", nullable = false, length = 64)
    private String email;

    @Column(name = "tel_number", length = 16)
    private String phoneNumber;

    @Column(name = "linked_in_profile", length = 255)
    private String linkedInProfile;

    @JoinColumn(name = "partner_id")
    @ManyToOne
    private PartnerEntity partner;

    @OneToMany(mappedBy = "subject", targetEntity = AttachmentEntity.class)
    private List<AttachmentEntity> attachments;

    @OneToMany(mappedBy = "subject", targetEntity = LogEntity.class)
    private List<LogEntity> logs;

    @OneToOne(mappedBy = "subject", targetEntity = TestEntity.class)
    private TestEntity test;

}

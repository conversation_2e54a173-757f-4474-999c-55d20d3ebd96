package sk.isdd.dr.jpa.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import sk.isdd.dr.jpa.entity.base.BaseEntity;

@Entity
@Table(name = "test")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TestEntity extends BaseEntity {

    @JoinColumn(name = "subject_id", updatable = false, unique = true)
    @OneToOne()
    private SubjectEntity subject;

    @Column(name = "cabinnet_result", updatable = false, columnDefinition = "LONGTEXT")
    private String cabinnetResult;

    @Column(name = "shl_test_link", updatable = false)
    private String shlTestLink;

    @Column(name = "shl_test_verify", updatable = false)
    private boolean shlTestVerify;

    @Column(name = "shl_test_receipt_id", updatable = false)
    private Integer shlTestReceiptId;

    @Column(name = "shl_tested_at")
    private LocalDateTime shlTestedAt;

    @Column(name = "shl_callback_hash", updatable = false)
    private String shlCallbackHash;

}

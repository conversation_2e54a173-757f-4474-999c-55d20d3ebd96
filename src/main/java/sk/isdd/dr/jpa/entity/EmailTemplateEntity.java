package sk.isdd.dr.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.jpa.entity.base.ModifiableEntity;

@Entity
@Table(name = "email_template")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EmailTemplateEntity extends ModifiableEntity {

    @Column(name = "code", columnDefinition = "VARCHAR(100)", nullable = false, length = 100)
    @Enumerated(EnumType.STRING)
    private EmailTemplateCode code;

    @Column(name = "subject", nullable = false, length = 255)
    private String subject;

    @Column(name = "title", nullable = false, length = 255)
    private String title;

    @Column(name = "template", nullable = false)
    private String template; // text in HTML

}

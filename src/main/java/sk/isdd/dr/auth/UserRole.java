package sk.isdd.dr.auth;

public enum UserRole {

    ADMIN("admin"),
    PARTNER_ADMIN("partner_admin"),
    COACH("coach"),
    COACH_ADMIN("coach_admin"),
    CLIENT("client"),
    CLIENT_REGISTERED("client_registered"),
    CLIENT_PRE_REGISTERED("client_pre_registered"),
    CLIENT_DECLINED("client_declined");

    private final String value;

    private UserRole(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static UserRole fromValue(String value) {
        for (UserRole role : UserRole.values()) {
            if (role.getValue().equalsIgnoreCase(value)) {
                return role;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

}

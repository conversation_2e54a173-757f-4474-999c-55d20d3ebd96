package sk.isdd.dr.auth;

import java.util.List;

import lombok.Data;

@Data
public class CurrentUser {
    
    private final String id;

    private final Integer subjectId;

    private final Integer partnerId;

    private final String email;

    private final String name;

    private final List<UserRole> roles;

    public boolean isClientPreRegistered() {
        return roles.contains(UserRole.CLIENT_PRE_REGISTERED);
    }

    public boolean isClient() {
        return roles.contains(UserRole.CLIENT);
    }

    public boolean isCoach() {
        return roles.contains(UserRole.COACH);
    }

    public boolean isCoachAdmin() {
        return roles.contains(UserRole.COACH_ADMIN);
    }

    public boolean isAdmin() {
        return roles.contains(UserRole.ADMIN);
    }

    public boolean isPartnerAdmin() {
        return roles.contains(UserRole.PARTNER_ADMIN);
    }

}

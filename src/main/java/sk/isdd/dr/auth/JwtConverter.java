package sk.isdd.dr.auth;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.JwtClaimNames;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.stereotype.Component;

import sk.isdd.dr.api.exception.system.SystemException;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.repository.SubjectRepository;

@Component
public class JwtConverter implements Converter<Jwt, UsernamePasswordAuthenticationToken> {

    private final JwtGrantedAuthoritiesConverter jwtGrantedAuthoritiesConverter = new JwtGrantedAuthoritiesConverter();

    @Autowired
    private SubjectRepository subjectRepository;

    @Override
    public UsernamePasswordAuthenticationToken convert(@NonNull Jwt jwt) {

        Collection<GrantedAuthority> authorities = Stream.concat(
                jwtGrantedAuthoritiesConverter.convert(jwt).stream(),
                extractResourceRoles(jwt).stream()).collect(Collectors.toSet());

        CurrentUser currentUser = buildCurrentUser(jwt);

        return new UsernamePasswordAuthenticationToken(
                currentUser,
                jwt,
                authorities);
    }

    private CurrentUser buildCurrentUser(Jwt jwt) {
        String id = jwt.getClaimAsString(JwtClaimNames.SUB);
        String email = jwt.getClaimAsString("email");
        String name = jwt.getClaimAsString("name");
        Integer partnerId = jwt.getClaimAsString("partner_id") == null ? null : Integer.parseInt(jwt.getClaimAsString("partner_id"));

        List<UserRole> roles = extractResourceRoles(jwt).stream()
                .map(GrantedAuthority::getAuthority)
                .map(role -> role.replace("ROLE_", ""))
                .filter(role -> role.equals("admin") || role.equals("partner_admin")
                        || role.equals("coach") || role.equals("coach_admin")
                        || role.equals("client") || role.equals("client_registered")
                        || role.equals("client_pre_registered"))
                .map(role -> UserRole.fromValue(role))
                .collect(Collectors.toList());

        boolean isCLient = roles
                .stream()
                .anyMatch((r) -> r == UserRole.CLIENT || r == UserRole.CLIENT_REGISTERED);

        SubjectEntity subject = subjectRepository.findByUserId(id).orElse(null);
        if (subject == null && isCLient) {
            throw new SystemException("Subject not found");
        }

        return new CurrentUser(id, subject == null ? null : subject.getId(), partnerId, email, name, roles);
    }

    private Collection<? extends GrantedAuthority> extractResourceRoles(Jwt jwt) {
        Map<String, Object> resourceAccess;
        Collection<String> resourceRoles;

        if (jwt.getClaim("realm_access") == null) {
            return Set.of();
        }

        resourceAccess = jwt.getClaim("realm_access");

        resourceRoles = (Collection<String>) resourceAccess.get("roles");

        return resourceRoles
                .stream()
                .map(role -> new SimpleGrantedAuthority("ROLE_" + role))
                .collect(Collectors.toSet());
    }
}
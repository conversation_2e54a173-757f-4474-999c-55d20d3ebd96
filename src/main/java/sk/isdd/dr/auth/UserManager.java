package sk.isdd.dr.auth;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.keycloak.admin.client.CreatedResponseUtil;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.RoleResource;
import org.keycloak.admin.client.resource.RolesResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.exception.business.BadRequestException;
import sk.isdd.dr.api.exception.system.SystemException;

@Service
@Validated
public class UserManager {

    private final Map<String, String> clientIdCache = new ConcurrentHashMap<>();

    private final Map<UserRole, RoleRepresentation> roleCache = new ConcurrentHashMap<>();

    @Autowired
    private Keycloak keycloakClient;

    @Value("${keycloak.realm}")
    private String keycloakRealmName;

    @Value("${keycloak.auth-client-id}")
    private String keycloakClientId;

    @Value("${keycloak.reset-password-link-lifespan}")
    private int resetPasswordLinkLifespan;

    @Value("${frontend-url}")
    private String feUrl;

    public Optional<UserRepresentation> _getUserById(String uuid) {
        try {
            return Optional.ofNullable(_toRepresentation(keycloakClient
                    .realm(keycloakRealmName)
                    .users()
                    .get(uuid)));
        } catch (NotFoundException e) {
            return Optional.empty();
        }
    }

    public Optional<UserRepresentation> _getUserByEmail(String email) {
        List<UserRepresentation> users = keycloakClient
                .realm(keycloakRealmName)
                .users()
                .search(email, 0, 1);

        if (users.isEmpty()) {
            return Optional.empty();
        }

        return _getUserById(users.getFirst().getId());
    }

    private UserRepresentation _toRepresentation(UserResource userResource) {
        UserRepresentation userRepresentation = userResource.toRepresentation();

        userRepresentation.setRealmRoles(userResource.roles().realmLevel().listEffective().stream()
                .map(RoleRepresentation::getName)
                .toList());

        userRepresentation.setCredentials(userResource.credentials());

        return userRepresentation;
    }

    public UserRepresentation changeClientPassword(String uuid, String oldPassword, String newPassword) {
        UserResource userResource = getUser(uuid);

        if (userResource.credentials().isEmpty()) {
            throw new BadRequestException("User has no credentials");
        }

        CredentialRepresentation newCR = new CredentialRepresentation();
        newCR.setTemporary(false);
        newCR.setValue(newPassword);
        newCR.setType(CredentialRepresentation.PASSWORD);

        try {
            userResource.resetPassword(newCR);
        } catch (WebApplicationException ex) {
            Response response = ex.getResponse();
            String errorMsg = response.readEntity(String.class);
            throw new BadRequestException("Password does not meet policy: " + errorMsg);
        }

        return userResource.toRepresentation();
    }

    public UserRepresentation createClient(String email, Integer partnerId, boolean retrySendEmail) {
        UserRepresentation user = getUserByEmail(email).orElse(null);

        if (user != null) {
            UserResource userResource = getUser(user.getId());

            boolean isPreregistered = userResource.roles().realmLevel().listAll().stream()
                    .anyMatch(role -> role.getName().equals(UserRole.CLIENT_PRE_REGISTERED.getValue()));

            List<CredentialRepresentation> credentials = userResource.credentials();

            boolean linkExpired = (LocalDateTime.now()
                    .atZone(ZoneId.systemDefault())
                    .toInstant()
                    .getEpochSecond() - user.getCreatedTimestamp()) > (resetPasswordLinkLifespan * 60 * 60);

            if ((credentials == null || credentials.isEmpty()) && isPreregistered && (linkExpired || retrySendEmail)) {
                // if password is not created already and link is expired, send email with link
                // again
                userResource.executeActionsEmail(keycloakClientId, feUrl, resetPasswordLinkLifespan * 60 * 60,
                        List.of("UPDATE_PASSWORD"));
                return user;
            }
            // else it is bad request
            // user has password or is deleted or in use
            throw new BadRequestException();
        }

        user = new UserRepresentation();
        user.setEnabled(true);
        user.setEmail(email);
        user.setUsername(email);
        user.setEmailVerified(false);

        if (partnerId != null) {
            user.setAttributes(new HashMap<>());
            user.getAttributes().put("partnerId", List.of(partnerId.toString()));
        }

        Response response = keycloakClient.realm(keycloakRealmName).users().create(user);
        if (response.getStatus() != 201) {
            throw new SystemException("Failed to create user: " + response.getStatusInfo());
        }

        String userId = CreatedResponseUtil.getCreatedId(response);
        UserResource userResource = getUser(userId);

        try {
            // assign role CLIENT_PRE_REGISTERED
            userResource.roles().realmLevel().add(List.of(getCachedRoleRepresentation(UserRole.CLIENT_PRE_REGISTERED)));
            // send UPDATE PASSWORD email
            userResource.executeActionsEmail(keycloakClientId, feUrl, resetPasswordLinkLifespan * 60 * 60,
                    List.of("UPDATE_PASSWORD"));

        } catch (Exception e) {
            // rollback user
            userResource.remove();
            throw new SystemException("User create failed");
        }

        return userResource.toRepresentation();
    }

    public UserRepresentation updateClient(Subject subject) {
        UserResource userResource = getUser(subject.getUserId());
        UserRepresentation userRepresentation = userResource.toRepresentation();

        if (!subject.getName().equals(userRepresentation.getFirstName()) && userRepresentation.getFirstName() == null) {
            userRepresentation.setFirstName(subject.getName());
        }

        if (!subject.getSurname().equals(userRepresentation.getLastName())
                && userRepresentation.getLastName() == null) {
            userRepresentation.setLastName(subject.getSurname());
        }

        userResource.update(userRepresentation);

        return userRepresentation;
    }

    public UserRepresentation finishRegistration(Subject subject) {
        if (subject.getUserId() == null) {
            throw new SystemException("User ID is empty");
        }

        UserResource userResource = getUser(subject.getUserId());
        UserRepresentation userRepresentation = userResource.toRepresentation();

        boolean isPreRegistered = userResource.roles().realmLevel().listAll().stream()
                .anyMatch(role -> role.getName().equals(UserRole.CLIENT_PRE_REGISTERED.getValue()));

        if (!isPreRegistered) {
            return userRepresentation;
        }

        if (!subject.getName().equals(userRepresentation.getFirstName()) && userRepresentation.getFirstName() == null) {
            userRepresentation.setFirstName(subject.getName());
        }

        if (!subject.getSurname().equals(userRepresentation.getLastName())
                && userRepresentation.getLastName() == null) {
            userRepresentation.setLastName(subject.getSurname());
        }

        userResource.roles().realmLevel().remove(List.of(getCachedRoleRepresentation(UserRole.CLIENT_PRE_REGISTERED)));
        userResource.roles().realmLevel().add(List.of(getCachedRoleRepresentation(UserRole.CLIENT_REGISTERED)));

        userResource.update(userRepresentation);

        return userRepresentation;
    }

    public UserRepresentation approveClient(Subject subject) {
        // set user role client
        UserResource userResource = getUser(subject.getUserId());
        userResource.roles().realmLevel().remove(List.of(getCachedRoleRepresentation(UserRole.CLIENT_REGISTERED)));
        userResource.roles().realmLevel().add(List.of(getCachedRoleRepresentation(UserRole.CLIENT)));
        return userResource.toRepresentation();
    }

    public UserRepresentation declineClient(Subject subject) {
        // set user role client_declined
        UserResource userResource = getUser(subject.getUserId());
        userResource.roles().realmLevel().remove(List.of(getCachedRoleRepresentation(UserRole.CLIENT_REGISTERED)));
        userResource.roles().realmLevel().add(List.of(getCachedRoleRepresentation(UserRole.CLIENT_DECLINED)));
        return userResource.toRepresentation();
    }

    public void deactiveClient(Subject subject, boolean fullDelete) {
        UserResource userResource = getUser(subject.getUserId());
        UserRepresentation userRepresentation = userResource.toRepresentation();

        if (fullDelete) {
            userResource.remove();
            return;
        }

        userRepresentation.setFirstName(null);
        userRepresentation.setLastName(null);
        userRepresentation.setEnabled(false);

        userResource.update(userRepresentation);

        userRepresentation.setCredentials(Collections.emptyList());
    }

    public Optional<UserRepresentation> getUserById(@NotNull String uuid) {
        try {
            UserRepresentation user = keycloakClient
                    .realm(keycloakRealmName)
                    .users()
                    .get(uuid)
                    .toRepresentation();

            return Optional.ofNullable(user);
        } catch (NotFoundException e) {
            return Optional.empty();
        }
    }

    public Optional<UserRepresentation> getUserByEmail(@Email String email) {
        List<UserRepresentation> users = keycloakClient
                .realm(keycloakRealmName)
                .users()
                .search(email, 0, 20); // max 20, default je veľa

        return users.stream()
                .filter(u -> email.equalsIgnoreCase(u.getEmail()))
                .findFirst();
    }

    public List<UserRepresentation> getUsersByRole(@NotNull UserRole role, Integer first, Integer size) {
        if (first != null && size != null) {
            return keycloakClient.realm(keycloakRealmName).roles().get(role.getValue()).getUserMembers(first, size);
        }
        return keycloakClient.realm(keycloakRealmName).roles().get(role.getValue()).getUserMembers();
    }

    public List<UserRepresentation> getUsersByRoleAndPartnerId(
            UserRole role,
            String partnerId,
            Integer first,
            Integer size) {

        // 1. Načítaj všetkých userov s danou rolou (bez paging z Keycloaku)
        List<UserRepresentation> usersWithRole = keycloakClient.realm(keycloakRealmName)
                .roles()
                .get(role.getValue())
                .getUserMembers(0, Integer.MAX_VALUE);

        if (partnerId != null) {
            // 2. Filtrovanie podľa partnerId
            usersWithRole = usersWithRole.stream()
                    .filter(u -> {
                        Map<String, List<String>> attrs = u.getAttributes();
                        return attrs != null
                                && attrs.containsKey("partnerId")
                                && attrs.get("partnerId") != null
                                && attrs.get("partnerId").contains(partnerId);
                    })
                    .collect(Collectors.toList());
        }

        // 3. Paging aplikovaný až po filtrovaní
        if (first != null && size != null) {
            int fromIndex = Math.max(0, first);
            int toIndex = Math.min(usersWithRole.size(), fromIndex + size);
            if (fromIndex >= usersWithRole.size()) {
                return Collections.emptyList();
            }
            return usersWithRole.subList(fromIndex, toIndex);
        }

        return usersWithRole;
    }

    public List<UserRepresentation> findPreRegisteredUsers(Integer first, Integer size) {
        RealmResource realm = keycloakClient.realm(keycloakRealmName);

        List<UserRepresentation> usersWithRole = getUsersByRole(UserRole.CLIENT_PRE_REGISTERED, first, size);

        UsersResource usersResource = realm.users();

        // filtrujeme podla credentials a email verified
        return usersWithRole.stream()
                .filter(UserRepresentation::isEmailVerified)
                .filter(u -> hasValidPassword(u, usersResource))
                .collect(Collectors.toList());
    }

    private boolean hasValidPassword(UserRepresentation user, UsersResource usersResource) {
        List<CredentialRepresentation> creds = usersResource.get(user.getId()).credentials();

        return creds.stream()
                .anyMatch(c -> "password".equals(c.getType()) && !c.isTemporary());
    }

    private boolean hasClientRole(String email, String roleName) {
        // 1. Najdi usera podľa emailu
        List<UserRepresentation> users = keycloakClient
                .realm(keycloakRealmName)
                .users()
                .search(email, 0, 1);

        if (users.isEmpty()) {
            return false; // user neexistuje
        }

        String userId = users.getFirst().getId();
        UserResource userResource = keycloakClient
                .realm(keycloakRealmName)
                .users()
                .get(userId);

        // 2. Skontroluj realm role
        return userResource.roles()
                .realmLevel()
                .listAll()
                .stream()
                .anyMatch(r -> r.getName().equals(roleName));
    }

    private UserResource getUser(String uuid) {
        UserResource userResource = keycloakClient
                .realm(keycloakRealmName)
                .users()
                .get(uuid);
        if (userResource == null) {
            throw new NotFoundException("User not found");
        }
        return userResource;
    }

    private RoleRepresentation getCachedRoleRepresentation(UserRole role) {
        return roleCache
                .computeIfAbsent(role, r -> {
                    return keycloakClient.realm(keycloakRealmName).roles().get(r.getValue()).toRepresentation();
                });
    }

}

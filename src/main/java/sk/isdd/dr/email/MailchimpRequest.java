package sk.isdd.dr.email;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MailchimpRequest {

    @JsonProperty(value = "key", required = true)
    private String key;

    @JsonProperty(value = "template_name", required = true)
    private String templateName;

    @JsonProperty(value = "template_content", required = true)
    private List<MergeVar> templateContent;

    @JsonProperty(value = "message", required = true)
    private Message message;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message {
        @JsonProperty(value = "from_email", required = true)
        private String fromEmail;
        private List<Recipient> to;
        private String subject;
        @JsonProperty(value = "merge_language", required = true)
        private String mergeLanguage = "handlebars";
        @JsonProperty(value = "global_merge_vars", required = true)
        private List<MergeVar> globalMergeVars;
        private List<EmailAttachment> attachments;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Recipient {
        private String email;
        private String type = "to";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MergeVar {
        private String name;
        private String content;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmailAttachment {
        private String type;
        private String name;
        private String content;
    }
}
package sk.isdd.dr.email;

import java.util.Map;

import lombok.Data;

@Data
public class EmailTemplateVariables {

    private final String text;

    private final String email;

    private final String password;

    public Map<String, String> toMap() {
        return Map.of(
            "text", text == null ? "" : text,
            "email", email == null ? "" : email,
            "password", password == null ? "" : password
        );
    }

}

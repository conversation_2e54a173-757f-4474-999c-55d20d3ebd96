package sk.isdd.dr.notification.email;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.jpa.repository.SubjectRepository;
import sk.isdd.dr.notification.email.api.EmailNotification;

@Component
public class BeginTestingNotification extends EmailNotification {

    @Autowired
    private SubjectRepository subjectRepository;

    @Value("${notification.begin-testing.threshold-days}")
    private int thresholdDays;

    @Override
    protected Set<String> getEmails() {
        return new HashSet<>(
                subjectRepository.getNotTestedSubjects(LocalDateTime.now().minusDays(thresholdDays)).stream()
                        .map(s -> s.getEmail()).toList());
    }

    @Override
    protected EmailTemplateCode getTemplateCode() {
        return EmailTemplateCode.NOTIFICATION_BEGIN_TESTING;
    }

}

package sk.isdd.dr.notification.email.api;

import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.email.MailchimpSenderService;
import sk.isdd.dr.notification.Notification;

public abstract class EmailNotification implements Notification {

    @Autowired
    private MailchimpSenderService mailSender;

    protected abstract Set<String> getEmails();

    protected abstract EmailTemplateCode getTemplateCode();

    @Override
    public void send() {
        Set<String> emails = getEmails();
        if (emails == null || emails.isEmpty()) {
            return;
        }
        emails.forEach(email -> mailSender.sendTemplateEmail(email, getTemplateCode(), null, null));
    }

}

package sk.isdd.dr.notification.email;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.jpa.repository.TestRepository;
import sk.isdd.dr.notification.email.api.EmailNotification;

@Component
public class FinishTestingNotification extends EmailNotification {

    @Autowired
    private TestRepository testRepository;

    @Value("${notification.finish-testing.threshold-days}")
    private int thresholdDays;

    @Override
    protected Set<String> getEmails() {
        return new HashSet<>(testRepository.getUnfinishedTests(LocalDateTime.now().minusDays(thresholdDays)).stream()
                .map(t -> t.getSubject().getEmail()).toList());
    }

    @Override
    protected EmailTemplateCode getTemplateCode() {
        return EmailTemplateCode.NOTIFICATION_FINISH_TESTING;
    }

}

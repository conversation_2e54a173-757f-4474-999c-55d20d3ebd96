server:
  port: 8080
  servlet:
    context-path: /api

spring:
  # main:
  #   allow-circular-references: true
  application:
    name: dr-api
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_URL}/realms/dr
  datasource:
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: org.mariadb.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: none # validate
    show-sql: false
    properties:
      hibernate:
        format_sql: true
  flyway:
    enabled: true
    baseline-on-migrate: true
    baseline-version: 1
    validate-on-migrate: true
  # mail:
  #   host: ${MAIL_URL}
  #   port: ${MAIL_PORT}
  #   username: ${MAIL_USERNAME}
  #   password: ${MAIL_PASSWORD}
  #   properties:
  #     mail:
  #       smtp:
  #         auth: true
  #         starttls:
  #           enabled: true
  #           required: true
  #   default-encoding: UTF-8

attachment:
  root-path: files/attachments

frontend-url: ${FRONTEND_URL}

shl:
  url: ${SHL_URL}
  process-candidate-registration:
    username: ${SHL_USERNAME}
    password: ${SHL_PASSWORD}
    party-id: ${SHL_PARTY_ID}
    integration-config-id: ${SHL_INTEGRATION_CONFIG_ID}
    partner-name: ${SHL_PARTNER_NAME}
    pushback-host: ${SHL_PUSHBACK_HOST}
    deadline-days: 14

keycloak:
  url: ${KEYCLOAK_URL}
  realm: dr
  client-id: dr-manager
  client-secret: ${KEYCLOAK_CLIENT_SECRET}
  auth-client-id: dr-auth
  reset-password-link-lifespan: 24 # in hours

springdoc:
  swagger-ui:
    path: /swagger-ui.html

chatbot:
  chatgpt:
    url: https://api.openai.com/v1/chat/completions
    model: gpt-4o
    key: ${CHATBOT_CHATGPT_KEY}
  claude:
    url: https://api.anthropic.com/v1/messages
    model: claude-3-5-sonnet-latest
    key: ${CHATBOT_CLAUDE_KEY}

mailchimp:
  api-url: https://mandrillapp.com/api/1.0/messages/send-template.json
  api-key: ${MAIL_API_KEY}
  from-email: <EMAIL>
  template-name: digiradca_general

notification:
  begin-testing:
    threshold-days: 10
  finish-testing:
    threshold-days: 10
  couching-options:
    threshold-days: 10

schedule:
  delete-declined-clients: "0 0 3 * * *" # kazdy den o 3:00
  notification-sender: "0 0 6 * * *" # kazdy den o 6:00

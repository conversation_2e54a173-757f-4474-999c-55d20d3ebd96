CREATE TABLE `test` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `created` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `subject_id` INT UNIQUE NOT NULL,
    `cabinnet_result` LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`cabinnet_result`)),
    `shl_test_link` VARCHAR(255) DEFAULT NULL,
    `shl_test_verify` BOOLEAN,
    `shl_test_receipt_id` INT DEFAULT NULL,
    `shl_tested_at` DATETIME DEFAULT NULL,
    `extra_data` LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`extra_data`)),
    
    PRIMAR<PERSON> KEY (`id`),
    CONSTRAINT `fk_test_subject`
        FOREIGN KEY (`subject_id`)
        REFERENCES `subject` (`id`)
);

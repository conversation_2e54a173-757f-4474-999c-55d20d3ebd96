-- schema from original implementation

CREATE TABLE `codebook` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created` datetime NOT NULL,
  `modified` datetime DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `type` varchar(32) NOT NULL,
  `code` varchar(16) NOT NULL,
  `title` varchar(64) NOT NULL,
  `subtitle` varchar(64) NOT NULL,
  `flags` int(11) NOT NULL,
  `extra_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`extra_data`)),
  PRIMARY KEY (`id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

CREATE TABLE `subject` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created` datetime NOT NULL,
  `modified` datetime DEFAULT NULL,
  `zoz_verified` datetime DEFAULT NULL,
  `city_id` int(11) DEFAULT NULL,
  `coach_id` int(11) DEFAULT NULL,
  `type` enum('person') NOT NULL,
  `name` varchar(64) NOT NULL,
  `surname` varchar(64) NOT NULL,
  `id_number` varchar(10) DEFAULT NULL,
  `email` varchar(64) NOT NULL,
  `tel_number` varchar(16) DEFAULT NULL,
  `flags` int(11) NOT NULL,
  `extra_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`extra_data`)),
  PRIMARY KEY (`id`),
  KEY `city_id` (`city_id`),
  KEY `zoz_verified` (`zoz_verified`),
  KEY `email` (`email`),
  KEY `id_number` (`id_number`),
  KEY `coach_id` (`coach_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created` datetime NOT NULL,
  `modified` datetime DEFAULT NULL,
  `deleted` datetime DEFAULT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `type` varchar(64) NOT NULL,
  `email` varchar(128) NOT NULL,
  `name` varchar(64) NOT NULL,
  `password` varchar(256) NOT NULL,
  `password_salt` varchar(256) NOT NULL,
  `access_token` varchar(256) DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `refresh_token` varchar(256) DEFAULT NULL,
  `flags` int(11) NOT NULL,
  `extra_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

CREATE TABLE `attachment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created` datetime NOT NULL,
  `modified` datetime DEFAULT NULL,
  `subject_id` int(11) NOT NULL,
  `type` varchar(32) NOT NULL,
  `mimetype` varchar(32) NOT NULL,
  `filename` varchar(128) NOT NULL,
  `flags` int(11) NOT NULL,
  `extra_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

CREATE TABLE `log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created` datetime NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `action` varchar(32) NOT NULL,
  `message` varchar(256) DEFAULT NULL,
  `extra_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

CREATE TABLE `score` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL,
  `subject_id` int(11) NOT NULL,
  `name` varchar(32) NOT NULL,
  `score` double(6,2) NOT NULL,
  `flags` int(11) NOT NULL,
  `extra_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`extra_data`)),
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;


-- subject -> city_id
ALTER TABLE `subject`
  ADD CONSTRAINT `subject_ibfk_1` FOREIGN KEY (`city_id`) REFERENCES `codebook` (`id`);

-- subject -> coach_id
ALTER TABLE `subject`
  ADD CONSTRAINT `subject_ibfk_2` FOREIGN KEY (`coach_id`) REFERENCES `user` (`id`);

-- user -> subject_id
ALTER TABLE `user`
  ADD CONSTRAINT `user_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subject` (`id`);

-- attachment -> subject_id
ALTER TABLE `attachment`
  ADD CONSTRAINT `attachment_ibfk_3` FOREIGN KEY (`subject_id`) REFERENCES `subject` (`id`);

-- log -> user_id
ALTER TABLE `log`
  ADD CONSTRAINT `log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`);

-- log -> subject_id
ALTER TABLE `log`
  ADD CONSTRAINT `log_ibfk_4` FOREIGN KEY (`subject_id`) REFERENCES `subject` (`id`);

-- score -> subject_id
ALTER TABLE `score`
  ADD CONSTRAINT `score_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subject` (`id`);

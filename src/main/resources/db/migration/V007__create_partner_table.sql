CREATE TABLE partner (
    id INT NOT NULL AUTO_INCREMENT,
    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified TIMESTAMP DEFAULT NULL,
    name VARCHAR(255) NOT NULL,
    logo VARCHAR(255) NOT NULL,
    flags INT,
    extra_data LONGTEXT,
    PRIMARY KEY (id)
);

ALTER TABLE subject
    ADD COLUMN partner_id INT AFTER coach_id,
    ADD CONSTRAINT fk_subject_partner
        FOREIGN KEY (partner_id) REFERENCES partner (id);

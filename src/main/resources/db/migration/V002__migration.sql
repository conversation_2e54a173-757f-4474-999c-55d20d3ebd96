-- first migration:
-- remove FKs to user table (users are in Keycloak) and change user_id type from int to varchar
-- rename enum values to UPPERCASE


-- attachment table

UPDATE attachment
SET type = 'CV' WHERE type = 'cv';

UPDATE attachment
SET type = 'FULL1' WHERE type = 'full1';

UPDATE attachment
SET type = 'FULL2' WHERE type = 'full2';

UPDATE attachment
SET type = 'SIMPLE' WHERE type = 'simple';

UPDATE attachment
SET type = 'COACH_REPORT' WHERE type = 'coachReport';

UPDATE attachment
SET type = 'COACH_REPORT_ENCRYPTED' WHERE type = 'coachReportEncrypted';

UPDATE attachment
SET type = 'SIMPLE_ENCRYPTED' WHERE type = 'simpleEncrypted';



-- subject table

ALTER TABLE `subject`
  MODIFY `coach_id` varchar(36) DEFAULT NULL,
  -- MODIFY `name` varchar(64) DEFAULT NULL,
  -- MODIFY `surname` varchar(64) DEFAULT NULL,
  DROP FOREIGN KEY subject_ibfk_2, -- do user_id (coach_id)
  ADD COLUMN `user_id` varchar(36) DEFAULT NULL AFTER `city_id`;



-- log table

ALTER TABLE `log`
  DROP FOREIGN KEY log_ibfk_1, -- do user_id (user_id)
  MODIFY `user_id` varchar(36) DEFAULT NULL;

UPDATE log SET action = 'REGISTER' WHERE action = 'register';
UPDATE log SET action = 'REGISTER_EMAIL_SENT' WHERE action = 'registerEmailSent';
UPDATE log SET action = 'ZOZ_VERIFICATION' WHERE action = 'zozVerification';
UPDATE log SET action = 'ATTACHMENT_ADDED' WHERE action = 'attachmentAdded';
UPDATE log SET action = 'ATTACHMENT_DELETED' WHERE action = 'attachmentDeleted';
UPDATE log SET action = 'COACH_REQUESTED' WHERE action = 'coachRequested';
UPDATE log SET action = 'COACH_ASSIGNED' WHERE action = 'coachAssigned';
UPDATE log SET action = 'USER_PASSWORD_CHANGE' WHERE action = 'userPasswordChange';
UPDATE log SET action = 'USER_PASSWORD_RESET' WHERE action = 'userPasswordReset';
UPDATE log SET action = 'USER_UPDATED' WHERE action = 'userUpdated';
UPDATE log SET action = 'USER_DELETED' WHERE action = 'userDeleted';
UPDATE log SET action = 'SUBJECT_UPDATED' WHERE action = 'subjectUpdated';
UPDATE log SET action = 'SUBJECT_DELETED' WHERE action = 'subjectDeleted';
UPDATE log SET action = 'SUBJECT_REPORTS_APPROVED' WHERE action = 'subjectReportsApproved';
UPDATE log SET action = 'LOGIN' WHERE action = 'login';
UPDATE log SET action = 'ADMIN_CREATE_USER' WHERE action = 'adminCreateUser';
UPDATE log SET action = 'REGISTRATION_DELETE' WHERE action = 'registrationDelete';
UPDATE log SET action = 'REGISTRATION_APPROVE' WHERE action = 'registrationApprove';
UPDATE log SET action = 'REGISTRATION_DECLINE' WHERE action = 'registrationDecline';
UPDATE log SET action = 'ACCOUNT_GENERATED' WHERE action = 'accountGenerated';
UPDATE log SET action = 'EMAIL_SENT' WHERE action = 'emailSent';
UPDATE log SET action = 'SHL_SCORE_IMPORTED' WHERE action = 'shlScoreImported';
UPDATE log SET action = 'SHL_JOB_MATCHING_IMPORTED' WHERE action = 'shlJobMatchingImported';
UPDATE log SET action = 'ACCOUNT_EMAIL_SENT' WHERE action = 'accountEmailSent';
UPDATE log SET action = 'ADMIN_LOGIN' WHERE action = 'adminLogin';
UPDATE log SET action = 'EMAIL_ZOZ_NOT_VERIFIED' WHERE action = 'emailZozNotVerified';
UPDATE log SET action = 'SHL_JOB_MEASUREMENT_IMPORTED' WHERE action = 'shlJobMeasurementImported';

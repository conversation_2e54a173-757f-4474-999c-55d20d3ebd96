variables:
  CI_DEBUG_TRACE: "true"
  # Trivy & AV security scan
  ENABLE_SCAN: "true"

  # Maven build configuration starts here
  CI_MAVEN_IMAGE: gitlab.isdd.sk:4567/isdd-internal/maven-java-images:1-0-0-maven3-9-7-temurinjdk21

  # Maven options for controlling logging and ensuring non-interactive build execution
  MAVEN_OPTS: "-Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"

  # Maven command-line options to reduce verbosity, handle errors, and ensure batch execution
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version"

  # Maven build options for specifying build profile, skipping tests, and integrating with SonarQube for code quality analysis
  MAVEN_BUILD_OPTS: "-DskipTests -Ddockerfile.skip -U sonar:sonar -Dsonar.projectName=veolia-api-integration-be -Dsonar.host.url=http://sonarq.isdd.sk"

  # Defines paths to artifacts to be collected after the build, tailored to the 'local' environment configuration
  ARTIFACT_PATH: "target/*.jar:target/classes/application.yml:pom.xml"

  # Príklady nastavenia build prostredia pre jednotlivé typy deploymentu
  # BUILD_ENV_ISDD_DEV: "isdd-dev"
  # BUILD_ENV_ISDD_TEST: "isdd-test"
  # BUILD_ENV_ISDD_PROD: "isdd-prod"
  # BUILD_ENV_ISDD_PRE_PROD: "isdd-pre-prod"

  PUSH_TO_NEXUS: "no"

# Každú z týchto premenných môžete ľubovoľne preťažiť podľa vašich potrieb, napríklad:
# BUILD_ENV_ISDD_DEV: "moje-dev-prostredie"

# Includes external YAML configuration from a project repository, allowing for modular pipeline definitions
include:
  - project: 'isdd-docker/templates/build-java-maven'
    ref: '2.0.3'
    file: '.gitlab-ci.yml'

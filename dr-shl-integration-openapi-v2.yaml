openapi: 3.0.3

info:
  title: DR integration API to SHL
  version: 0.0.1

servers:
  - url: https://test.digitalnyradca.sk/api
    description: Test server
  - url: http://localhost:8080/api
    description: Local server

paths:
  /api/shl/import-report:
    post:
      summary: Upload final report to DR NEET
      security:
        - customBearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UploadPDFReportRequest'
      responses:
        '200':
          description: Report upload success
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [OK]
                    description: Stav spracovania požiadavky
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Subject with email address not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/shl/cabinnet/result:
    get:
      summary: Acquire CABIN-NET test result and level of education of subject specified by email
      security:
        - customBearerAuth: []
      parameters:
        - in: query
          name: email
          required: true
          schema:
            type: string
            format: email
          description: Email of the subject
      responses:
        '200':
          description: Cabinnet test result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CabinnetTest'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Subject with email address or Cabinnet result not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/shl/cabinnet/participants:
    get:
      summary: Acquire emails and names of participants
      parameters:
        - in: query
          name: time_from
          required: true
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Cabinet test participants
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCabinnetTestParticipantsResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    customBearerAuth:
      type: http
      scheme: bearer
      bearerFormat: sha256-hash
      description: Custom authentication token generated as SHA-256 hash of a random UID.
  
  schemas:
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Description of the error

    CabinnetTest:
      type: object
      properties:
        created_at:
          type: string
          format: date-time
        education_level:
          $ref: '#/components/schemas/SubjectEducationLevel'
        results:
          type: array
          items:
            $ref: '#/components/schemas/CabinnetTestQuestionAnswer'
      required:
        - created_at
        - education_level
        - results

    CabinnetTestQuestionAnswer:
      type: object
      properties:
        question_id:
          type: integer
          description: ID of question
        answer:
          type: integer
          minimum: 1
          maximum: 5
          description: Answer to the question
      required:
        - question_id
        - answer

    SubjectEducationLevel:
      type: string
      enum:
        - ELEMENTARY
        - SECONDARY_VOCATIONAL
        - SECONDARY_COMPLETE
        - POST_SECONDARY
        - BACHELOR
        - MASTER_OR_HIGHER

    GetCabinnetTestParticipantsResponse:
      type: object
      properties:
        participants:
          type: array
          items:
            $ref: '#/components/schemas/CabinnetTestParticipant'
      required:
        - participants
    
    UploadPDFReportRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: Email address of the subject
        report:
          type: string
          format: binary
          description: Report in PDF file format
      required:
        - email
        - report

    CabinnetTestParticipant:
      type: object
      properties:
        name:
          type: string
        email:
          type: string
          format: email
      required:
        - name
        - email

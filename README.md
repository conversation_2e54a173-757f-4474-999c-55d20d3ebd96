Note: replace blocks of text in [] with appropriate data for your project. Remove, if not applicable.
# [Project name]

[description and purpose of the project, list of core technologies used - react, typescript, java, spring...]

## Table of contents
1. [Tools and prerequisites](#tools-and-prerequisites)
1. [Specifications, access and passwords](#specifications-access-and-passwords)
1. [Project setup](#project-setup)
1. [Testing](#testing)
1. [Development and Debug](#development-and-debug)
1. [Code structure](#code-structure)
1. [Build and release](#build-and-release)
1. [Scripts and utils](#scripts-and-utils)

## Tools and prerequisites
- [required hardware]
- [required/recommended IDE, it's recommended configuration, plugins...]
- [required and recommended tools and their versions - cocoapods, node, yarn, npm, docker, jre, jvm, database, browser, git client...]

## Specifications, access and passwords
- [links to specs (confluence, figma, word document, share point...) or project documentation]
- [links to confluence page with passwords, keystores, certificates]
- [link to appstore, google play, jenkins]
- [names of people who can grant access]

## Project setup
- [libraries and dependencies installation (ideally add exact console commands and links to download)]
- [exact commands and outputs to double check whether the dependency and it's correct version is installed]
- [how to create .env file (ideally attach template) when needed]
- [troubleshooting - list of common problems during project setup and their solutions]

## Testing
- [how to setup testing environment (mocha, chai, jest...), similar things as in project setup section]
- [commands used to run tests]
- [how to process test outputs]

## Development and Debug
- [styleguide - best practices, conventions and customs, variable naming, usage of patterns (functional components over class components in react, boolean variables must be named isVariable or hasVariable, etc.)]
- [how to use enforced dev rules like eslint, prettier or similar alternative in java]
- [links to recommended sources which let to application of these guidelines]
- [code review guide]
- [pull request guide (what should be present in pull request, self-check, commit message, git branch naming, etc.)]
- [how to debug, what tools are needed, what approach is recommended, tips and tricks]
- [how to work with more complex or not so intuitive systems (dynamic forms, translations, push notifications system...)]
- [how to test/dev some features which require working with other systems (use web console to send marketing notification for example)]

### Code structure
- [file and folder structure, cheatsheet what to find where (where are common components, screens, hooks, apis, utils...)]

### Build and release
- [github/gitlab actions description]
- [docker configuration]
- [manual build/release process]

### Scripts and utils
- [description and usage of helper scripts or tools if there are any present]
